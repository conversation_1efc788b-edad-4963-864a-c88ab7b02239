{% if item %}
    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">{{ item.name }}</h4>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {{ item.item_code }}
            </span>
        </div>
        
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-gray-500">Category:</span>
                <span class="ml-1 font-medium text-gray-900">{{ item.category.name }}</span>
            </div>
            <div>
                <span class="text-gray-500">Unit:</span>
                <span class="ml-1 font-medium text-gray-900">{{ item.unit_of_measure }}</span>
            </div>
            <div>
                <span class="text-gray-500">Current Stock:</span>
                <span class="ml-1 font-medium {{ stock_class }}">{{ item.current_stock }} units</span>
            </div>
            <div>
                <span class="text-gray-500">Min. Threshold:</span>
                <span class="ml-1 font-medium text-gray-900">{{ item.minimum_threshold }} units</span>
            </div>
            <div>
                <span class="text-gray-500">Unit Cost:</span>
                <span class="ml-1 font-medium text-gray-900">₱{{ item.unit_cost|floatformat:2 }}</span>
            </div>
            <div>
                <span class="text-gray-500">Status:</span>
                <span class="ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                    {% if item.current_stock == 0 %}bg-red-100 text-red-800
                    {% elif item.is_low_stock %}bg-yellow-100 text-yellow-800
                    {% else %}bg-green-100 text-green-800{% endif %}">
                    {{ stock_status }}
                </span>
            </div>
        </div>
        
        {% if item.description %}
            <div class="pt-2 border-t border-gray-200">
                <span class="text-gray-500 text-sm">Description:</span>
                <p class="mt-1 text-sm text-gray-700">{{ item.description }}</p>
            </div>
        {% endif %}
    </div>
{% endif %}