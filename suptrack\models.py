from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid


class UserProfile(models.Model):
    """Extended user profile with role and department information"""
    ROLE_CHOICES = [
        ('admin', 'Administrator'),
        ('gso_staff', 'GSO Staff'),
        ('department_user', 'Department User'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
    
    class Meta:
        db_table = 'user_profiles'


class Category(models.Model):
    """Supply item categories for organization"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        db_table = 'categories'
        verbose_name_plural = 'Categories'


class SupplyItem(models.Model):
    """Supply items with inventory tracking"""
    item_code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    unit_of_measure = models.CharField(max_length=20)
    current_stock = models.PositiveIntegerField(default=0)
    minimum_threshold = models.PositiveIntegerField(default=10, validators=[MinValueValidator(0)])
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    supplier = models.CharField(max_length=200)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.item_code} - {self.name}"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum threshold"""
        return self.current_stock <= self.minimum_threshold
    
    @property
    def total_value(self):
        """Calculate total inventory value"""
        return self.current_stock * self.unit_cost
    
    class Meta:
        db_table = 'supply_items'
        indexes = [
            models.Index(fields=['item_code']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['current_stock']),
        ]


class SupplyRequest(models.Model):
    """Supply requests from departments"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('released', 'Released'),
        ('completed', 'Completed'),
    ]
    
    request_id = models.CharField(max_length=20, unique=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='supply_requests')
    department = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    justification = models.TextField()
    requested_date = models.DateTimeField(auto_now_add=True)
    required_date = models.DateField()
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_requests')
    approved_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.request_id} - {self.requester.username}"
    
    def save(self, *args, **kwargs):
        if not self.request_id:
            # Generate unique request ID
            self.request_id = f"REQ-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)
    
    @property
    def total_items(self):
        """Get total number of items in request"""
        return self.request_items.count()
    
    @property
    def total_cost(self):
        """Calculate total estimated cost of request"""
        return sum(item.total_cost for item in self.request_items.all())
    
    class Meta:
        db_table = 'supply_requests'
        indexes = [
            models.Index(fields=['request_id']),
            models.Index(fields=['requester', 'status']),
            models.Index(fields=['status', 'requested_date']),
        ]


class RequestItem(models.Model):
    """Individual items within a supply request"""
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='request_items')
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity_requested = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    quantity_approved = models.PositiveIntegerField(null=True, blank=True, validators=[MinValueValidator(0)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.request.request_id} - {self.supply_item.name}"
    
    @property
    def total_cost(self):
        """Calculate total cost for requested quantity"""
        return self.quantity_requested * self.unit_price
    
    @property
    def approved_total_cost(self):
        """Calculate total cost for approved quantity"""
        if self.quantity_approved is not None:
            return self.quantity_approved * self.unit_price
        return 0
    
    def save(self, *args, **kwargs):
        # Set unit price from supply item if not provided
        if not self.unit_price:
            self.unit_price = self.supply_item.unit_cost
        super().save(*args, **kwargs)
    
    class Meta:
        db_table = 'request_items'
        unique_together = ['request', 'supply_item']
        indexes = [
            models.Index(fields=['request', 'supply_item']),
        ]


class StockTransaction(models.Model):
    """Track all inventory movements"""
    TRANSACTION_TYPES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Stock Adjustment'),
        ('return', 'Return'),
        ('damaged', 'Damaged/Lost'),
    ]
    
    transaction_id = models.CharField(max_length=20, unique=True)
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()  # Can be negative for outgoing transactions
    reference_document = models.CharField(max_length=100, blank=True)
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
    request_item = models.ForeignKey(RequestItem, on_delete=models.SET_NULL, null=True, blank=True)
    
    def __str__(self):
        return f"{self.transaction_id} - {self.supply_item.name} ({self.quantity})"
    
    def save(self, *args, **kwargs):
        if not self.transaction_id:
            # Generate unique transaction ID
            self.transaction_id = f"TXN-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)
        
        # Update supply item stock after saving transaction
        if self.pk:  # Only update if transaction is saved
            self.update_stock()
    
    def update_stock(self):
        """Update supply item stock based on transaction"""
        if self.transaction_type in ['in', 'return']:
            self.supply_item.current_stock += abs(self.quantity)
        elif self.transaction_type in ['out', 'damaged']:
            self.supply_item.current_stock = max(0, self.supply_item.current_stock - abs(self.quantity))
        elif self.transaction_type == 'adjustment':
            # For adjustments, quantity can be positive or negative
            new_stock = self.supply_item.current_stock + self.quantity
            self.supply_item.current_stock = max(0, new_stock)
        
        self.supply_item.save()
    
    class Meta:
        db_table = 'stock_transactions'
        indexes = [
            models.Index(fields=['transaction_id']),
            models.Index(fields=['supply_item', 'timestamp']),
            models.Index(fields=['transaction_type', 'timestamp']),
        ]


class QRCode(models.Model):
    """QR codes for supply item tracking"""
    code_id = models.CharField(max_length=50, unique=True)
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='qr_codes')
    request_item = models.ForeignKey(RequestItem, on_delete=models.CASCADE, related_name='qr_codes')
    qr_image = models.ImageField(upload_to='qr_codes/', blank=True)
    generated_date = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    scanned_count = models.PositiveIntegerField(default=0)
    last_scanned = models.DateTimeField(null=True, blank=True)
    last_scanned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='scanned_qr_codes')
    
    def __str__(self):
        return f"QR-{self.code_id} - {self.supply_item.name}"
    
    def save(self, *args, **kwargs):
        if not self.code_id:
            # Generate unique QR code ID
            self.code_id = f"{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:12].upper()}"
        super().save(*args, **kwargs)
    
    def record_scan(self, user):
        """Record a QR code scan"""
        self.scanned_count += 1
        self.last_scanned = timezone.now()
        self.last_scanned_by = user
        self.save()
    
    class Meta:
        db_table = 'qr_codes'
        indexes = [
            models.Index(fields=['code_id']),
            models.Index(fields=['supply_item', 'is_active']),
            models.Index(fields=['request_item']),
        ]


class ScanActivity(models.Model):
    """Track QR code scan activities"""
    SCAN_TYPES = [
        ('general', 'General Scan'),
        ('issuance', 'Item Issuance'),
        ('return', 'Item Return'),
        ('audit', 'Inventory Audit'),
    ]
    
    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, null=True, blank=True, related_name='scan_activities')
    scanned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scan_activities')
    scan_type = models.CharField(max_length=20, choices=SCAN_TYPES, default='general')
    scanned_at = models.DateTimeField(auto_now_add=True)
    scan_data = models.JSONField(default=dict, blank=True)
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    def __str__(self):
        qr_info = f"QR-{self.qr_code.code_id}" if self.qr_code else "Unknown QR"
        return f"{qr_info} - {self.get_scan_type_display()} by {self.scanned_by.username}"
    
    class Meta:
        db_table = 'scan_activities'
        indexes = [
            models.Index(fields=['qr_code', 'scanned_at']),
            models.Index(fields=['scanned_by', 'scanned_at']),
            models.Index(fields=['scan_type', 'scanned_at']),
        ]
