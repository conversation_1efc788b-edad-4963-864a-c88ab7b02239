# Design Document

## Overview

The Smart Supply Management Information System is a Django-based web application that digitizes supply chain operations at JHCSC Dumingag Campus. The system follows a mobile-first, responsive design approach using Django for backend operations, Tailwind CSS for styling, HTMX for dynamic interactions, Alpine.js for client-side interactivity, and Unpoly for seamless navigation.

The architecture emphasizes user experience optimization for users with varying technical expertise while maintaining robust security and data integrity through Django's built-in features and additional security measures.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Mobile Browser] --> B[Desktop Browser]
        B --> C[Tablet Browser]
    end
    
    subgraph "Frontend Layer"
        D[Tailwind CSS] --> E[HTMX]
        E --> F[Alpine.js]
        F --> G[Unpoly]
        G --> H[Three.js CDN]
    end
    
    subgraph "Django Application Layer"
        I[Authentication & Authorization]
        J[Supply Request Views]
        K[Approval Workflow Views]
        L[Inventory Management Views]
        M[QR Code Generation]
        N[Reporting Engine]
        O[API Endpoints for HTMX]
    end
    
    subgraph "Data Layer"
        P[SQLite Database]
        Q[Media Storage for QR Codes]
        R[Static Files]
    end
    
    A --> D
    B --> D
    C --> D
    
    I --> P
    J --> P
    K --> P
    L --> P
    M --> Q
    N --> P
    O --> P
```

### Technology Stack Integration

- **Django 5.2.3**: Backend framework handling authentication, business logic, and data persistence
- **SQLite**: Database for development and small-scale deployment
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **HTMX**: Enables partial page updates and dynamic interactions without JavaScript
- **Alpine.js**: Lightweight JavaScript framework for interactive components
- **Unpoly**: Progressive enhancement for fast navigation
- **Python QRCode Library**: Server-side QR code generation
- **ReportLab/WeasyPrint**: PDF report generation

## Components and Interfaces

### Authentication System

**Extended User Model**
```python
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    is_active = models.BooleanField(default=True)
```

**Role-Based Access Control**
- Custom decorators for view-level permissions
- Template context processors for role-based UI rendering
- Middleware for automatic role verification

### Supply Request Module

**Request Management Interface**
```python
class SupplyRequest(models.Model):
    request_id = models.CharField(max_length=20, unique=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE)
    department = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    justification = models.TextField()
    requested_date = models.DateTimeField(auto_now_add=True)
    required_date = models.DateField()
    approved_by = models.ForeignKey(User, null=True, related_name='approved_requests')
    approved_date = models.DateTimeField(null=True)
```

**Request Item Details**
```python
class RequestItem(models.Model):
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE)
    supply_item = models.ForeignKey('SupplyItem', on_delete=models.CASCADE)
    quantity_requested = models.PositiveIntegerField()
    quantity_approved = models.PositiveIntegerField(null=True)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
```

### Inventory Management System

**Supply Item Model**
```python
class SupplyItem(models.Model):
    item_code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField()
    category = models.ForeignKey('Category', on_delete=models.CASCADE)
    unit_of_measure = models.CharField(max_length=20)
    current_stock = models.PositiveIntegerField(default=0)
    minimum_threshold = models.PositiveIntegerField(default=10)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2)
    supplier = models.CharField(max_length=200)
    last_updated = models.DateTimeField(auto_now=True)
```

**Stock Transaction Tracking**
```python
class StockTransaction(models.Model):
    transaction_id = models.CharField(max_length=20, unique=True)
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()
    reference_document = models.CharField(max_length=100)
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
```

### QR Code System

**QR Code Model**
```python
class QRCode(models.Model):
    code_id = models.CharField(max_length=50, unique=True)
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    request_item = models.ForeignKey(RequestItem, on_delete=models.CASCADE)
    qr_image = models.ImageField(upload_to='qr_codes/')
    generated_date = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    scanned_count = models.PositiveIntegerField(default=0)
    last_scanned = models.DateTimeField(null=True)
```

**QR Code Generation Service**
```python
class QRCodeService:
    @staticmethod
    def generate_qr_code(request_item):
        # Generate unique QR code data
        # Create QR code image using qrcode library
        # Save to media storage
        # Return QRCode model instance
```

### Mobile-Responsive Interface Design

**Navigation Component (Alpine.js)**
```html
<div x-data="{ sidebarOpen: false }" class="flex h-screen bg-gray-100">
    <!-- Mobile sidebar toggle -->
    <button @click="sidebarOpen = !sidebarOpen" 
            class="lg:hidden fixed top-4 left-4 z-50">
        <svg class="w-6 h-6">...</svg>
    </button>
    
    <!-- Collapsible sidebar -->
    <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'" 
         class="fixed lg:static lg:translate-x-0 transition-transform">
        <!-- Navigation items -->
    </div>
</div>
```

**HTMX Integration for Dynamic Updates**
```html
<!-- Supply request form with HTMX -->
<form hx-post="/requests/create/" 
      hx-target="#request-list" 
      hx-swap="afterbegin">
    <!-- Form fields -->
</form>

<!-- Real-time inventory updates -->
<div hx-get="/inventory/status/" 
     hx-trigger="every 30s" 
     hx-target="#inventory-dashboard">
    <!-- Inventory display -->
</div>
```

### Reporting System

**Report Generation Interface**
```python
class ReportGenerator:
    def generate_supply_request_report(self, date_range, format='pdf'):
        # Query supply requests within date range
        # Format data for report
        # Generate PDF using ReportLab or CSV using Python csv module
        
    def generate_inventory_report(self, categories=None, format='pdf'):
        # Query inventory data
        # Include stock levels, transactions, alerts
        # Generate formatted report
```

## Data Models

### Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ SupplyRequest : creates
    User ||--o{ StockTransaction : performs
    User ||--|| UserProfile : has
    
    SupplyRequest ||--o{ RequestItem : contains
    RequestItem ||--|| SupplyItem : references
    RequestItem ||--o| QRCode : generates
    
    SupplyItem ||--o{ StockTransaction : tracks
    SupplyItem ||--|| Category : belongs_to
    
    Category ||--o{ SupplyItem : contains
    
    QRCode ||--|| SupplyItem : identifies
    QRCode ||--|| RequestItem : links_to
```

### Database Schema Considerations

**Indexing Strategy**
- Primary keys on all tables
- Foreign key indexes for relationship queries
- Composite indexes on frequently queried combinations (user + status, item + date)
- Full-text search indexes on item names and descriptions

**Data Integrity Constraints**
- Foreign key constraints with appropriate CASCADE/PROTECT settings
- Check constraints for positive quantities and valid status transitions
- Unique constraints on business identifiers (request_id, item_code, qr_code_id)

## Error Handling

### Client-Side Error Handling

**HTMX Error Responses**
```javascript
document.body.addEventListener('htmx:responseError', function(evt) {
    // Display user-friendly error messages
    // Log errors for debugging
    // Provide retry mechanisms
});
```

**Alpine.js Error States**
```html
<div x-data="{ loading: false, error: null }">
    <div x-show="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <span x-text="error"></span>
    </div>
</div>
```

### Server-Side Error Handling

**Django Exception Handling**
```python
class SupplyRequestView(View):
    def post(self, request):
        try:
            # Process request
            pass
        except ValidationError as e:
            return JsonResponse({'error': str(e)}, status=400)
        except IntegrityError as e:
            return JsonResponse({'error': 'Data integrity error'}, status=500)
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return JsonResponse({'error': 'System error occurred'}, status=500)
```

**Custom Error Pages**
- 404: Item not found with search suggestions
- 403: Access denied with role information
- 500: System error with support contact information

## Testing Strategy

### Unit Testing

**Model Testing**
```python
class SupplyItemModelTest(TestCase):
    def test_stock_reduction_validation(self):
        # Test stock cannot go below zero
        # Test minimum threshold alerts
        
    def test_qr_code_generation(self):
        # Test unique QR code creation
        # Test QR code data integrity
```

**View Testing**
```python
class SupplyRequestViewTest(TestCase):
    def test_authenticated_access_only(self):
        # Test login required for all views
        
    def test_role_based_permissions(self):
        # Test different user roles access appropriate features
```

### Integration Testing

**HTMX Interaction Testing**
```python
class HTMXIntegrationTest(TestCase):
    def test_partial_page_updates(self):
        # Test HTMX requests return correct HTML fragments
        # Test dynamic form submissions
```

**QR Code Workflow Testing**
```python
class QRCodeWorkflowTest(TestCase):
    def test_end_to_end_qr_process(self):
        # Test QR generation → scanning → inventory update
```

### Mobile Responsiveness Testing

**Responsive Design Testing**
- Automated testing with Selenium for different viewport sizes
- Touch interaction testing for mobile gestures
- Performance testing for mobile network conditions

### Security Testing

**Authentication Testing**
- Session management testing
- CSRF protection verification
- SQL injection prevention testing
- XSS protection validation

**Authorization Testing**
- Role-based access control verification
- Privilege escalation prevention testing
- Data access boundary testing

### Performance Testing

**Database Performance**
- Query optimization testing
- Index effectiveness measurement
- Transaction performance under load

**Frontend Performance**
- Page load time measurement
- HTMX request/response timing
- Mobile performance optimization validation

This design provides a comprehensive foundation for implementing the Smart Supply Management Information System with all required features while maintaining security, usability, and performance standards.