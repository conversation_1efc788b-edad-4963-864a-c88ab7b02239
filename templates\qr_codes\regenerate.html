{% extends 'base.html' %}
{% load static %}

{% block title %}Regenerate QR Code - {{ qr_code.code_id }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Regenerate QR Code</h1>
            <p class="mt-2 text-gray-600">Create a new QR code to replace a damaged or lost one</p>
        </div>

        <!-- Warning Notice -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Important Notice</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Regenerating will deactivate the current QR code</li>
                            <li>A new QR code with a different ID will be created</li>
                            <li>The old QR code will no longer be scannable</li>
                            <li>All tracking data will be preserved and linked to the new code</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current QR Code Information -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Current QR Code</h2>
            </div>
            <div class="p-6">
                <div class="flex items-start space-x-6">
                    <!-- QR Code Image -->
                    <div class="flex-shrink-0">
                        {% if qr_code.qr_image %}
                            <img src="{{ qr_code.qr_image.url }}" 
                                 alt="Current QR Code" 
                                 class="w-24 h-24 border border-gray-300 rounded">
                        {% endif %}
                        <div class="text-center mt-2">
                            <div class="text-sm font-mono text-gray-700">{{ qr_code.code_id }}</div>
                        </div>
                    </div>

                    <!-- QR Code Details -->
                    <div class="flex-1">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Item Name</label>
                                <p class="text-sm text-gray-900">{{ qr_code.supply_item.name }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Item Code</label>
                                <p class="text-sm text-gray-900 font-mono">{{ qr_code.supply_item.item_code }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Request ID</label>
                                <p class="text-sm text-gray-900 font-mono">{{ qr_code.request_item.request.request_id }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Department</label>
                                <p class="text-sm text-gray-900">{{ qr_code.request_item.request.department }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Generated Date</label>
                                <p class="text-sm text-gray-900">{{ qr_code.generated_date|date:"M d, Y H:i" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">Scan Count</label>
                                <p class="text-sm text-gray-900">{{ qr_code.scanned_count }} scans</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Regeneration Reasons -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Common Reasons for Regeneration</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-red-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-gray-900">Damaged QR Code</h3>
                            <p class="text-sm text-gray-500">Physical damage making the code unreadable</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-eye-slash text-orange-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-gray-900">Faded Print</h3>
                            <p class="text-sm text-gray-500">Poor print quality or fading over time</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-search text-blue-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-gray-900">Lost Label</h3>
                            <p class="text-sm text-gray-500">Physical label has been lost or removed</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-mobile-alt text-green-500 mt-1"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-gray-900">Scanning Issues</h3>
                            <p class="text-sm text-gray-500">Technical problems with scanning the current code</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Form -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Confirm Regeneration</h2>
            </div>
            <div class="p-6">
                <form method="POST">
                    {% csrf_token %}
                    
                    <div class="mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="confirm_regeneration" 
                                   name="confirm_regeneration" 
                                   required
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="confirm_regeneration" class="ml-2 text-sm text-gray-700">
                                I understand that regenerating this QR code will deactivate the current code and create a new one
                            </label>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <a href="{% url 'qr_code_detail' qr_code.code_id %}" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-redo mr-2"></i>
                            Regenerate QR Code
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Process Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">What Happens Next</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ol class="list-decimal list-inside space-y-1">
                            <li>Current QR code will be marked as inactive</li>
                            <li>New QR code will be generated with fresh ID</li>
                            <li>All item and request data will be preserved</li>
                            <li>You'll be redirected to the new QR code details</li>
                            <li>Print new labels as needed</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}