from django.contrib import admin
from .models import UserProfile, Category, SupplyItem, SupplyRequest, RequestItem, StockTransaction, QRCode


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'department', 'is_active', 'created_at']
    list_filter = ['role', 'department', 'is_active']
    search_fields = ['user__username', 'user__email', 'department']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']


@admin.register(SupplyItem)
class SupplyItemAdmin(admin.ModelAdmin):
    list_display = ['item_code', 'name', 'category', 'current_stock', 'minimum_threshold', 'is_low_stock', 'is_active']
    list_filter = ['category', 'is_active', 'current_stock']
    search_fields = ['item_code', 'name', 'description', 'supplier']
    readonly_fields = ['created_at', 'last_updated']
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'Low Stock'


@admin.register(SupplyRequest)
class SupplyRequestAdmin(admin.ModelAdmin):
    list_display = ['request_id', 'requester', 'department', 'status', 'requested_date', 'required_date']
    list_filter = ['status', 'department', 'requested_date']
    search_fields = ['request_id', 'requester__username', 'department', 'justification']
    readonly_fields = ['request_id', 'requested_date']
    date_hierarchy = 'requested_date'


@admin.register(RequestItem)
class RequestItemAdmin(admin.ModelAdmin):
    list_display = ['request', 'supply_item', 'quantity_requested', 'quantity_approved', 'unit_price']
    list_filter = ['request__status', 'supply_item__category']
    search_fields = ['request__request_id', 'supply_item__name']


@admin.register(StockTransaction)
class StockTransactionAdmin(admin.ModelAdmin):
    list_display = ['transaction_id', 'supply_item', 'transaction_type', 'quantity', 'performed_by', 'timestamp']
    list_filter = ['transaction_type', 'timestamp', 'supply_item__category']
    search_fields = ['transaction_id', 'supply_item__name', 'reference_document']
    readonly_fields = ['transaction_id', 'timestamp']
    date_hierarchy = 'timestamp'


@admin.register(QRCode)
class QRCodeAdmin(admin.ModelAdmin):
    list_display = ['code_id', 'supply_item', 'request_item', 'is_active', 'scanned_count', 'generated_date']
    list_filter = ['is_active', 'generated_date', 'supply_item__category']
    search_fields = ['code_id', 'supply_item__name']
    readonly_fields = ['code_id', 'generated_date', 'scanned_count', 'last_scanned']
