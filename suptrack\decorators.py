from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponseForbidden
from django.template.loader import render_to_string


def role_required(*allowed_roles):
    """
    Decorator to restrict access based on user roles.
    Usage: @role_required('admin', 'gso_staff')
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            try:
                user_profile = request.user.userprofile
                if not user_profile.is_active:
                    messages.error(request, "Your account has been deactivated. Please contact an administrator.")
                    return redirect('login')
                
                if user_profile.role in allowed_roles:
                    return view_func(request, *args, **kwargs)
                else:
                    # Return 403 Forbidden with custom template
                    context = {
                        'user_role': user_profile.get_role_display(),
                        'required_roles': [dict(user_profile.ROLE_CHOICES).get(role, role) for role in allowed_roles],
                        'message': f"Access denied. This feature requires {' or '.join([dict(user_profile.ROLE_CHOICES).get(role, role) for role in allowed_roles])} privileges."
                    }
                    return HttpResponseForbidden(
                        render_to_string('auth/403.html', context, request=request)
                    )
            except AttributeError:
                messages.error(request, "User profile not found. Please contact an administrator.")
                return redirect('login')
        return _wrapped_view
    return decorator


def admin_required(view_func):
    """Decorator to restrict access to admin users only"""
    return role_required('admin')(view_func)


def gso_staff_required(view_func):
    """Decorator to restrict access to GSO staff and admin users"""
    return role_required('admin', 'gso_staff')(view_func)


def department_user_required(view_func):
    """Decorator to restrict access to department users and above"""
    return role_required('admin', 'gso_staff', 'department_user')(view_func)


def check_user_role(user, required_roles):
    """
    Helper function to check if user has required role.
    Returns tuple (has_permission, user_role, error_message)
    """
    try:
        user_profile = user.userprofile
        if not user_profile.is_active:
            return False, None, "Account is deactivated"
        
        if user_profile.role in required_roles:
            return True, user_profile.role, None
        else:
            role_names = [dict(user_profile.ROLE_CHOICES).get(role, role) for role in required_roles]
            return False, user_profile.role, f"Requires {' or '.join(role_names)} privileges"
    except AttributeError:
        return False, None, "User profile not found"


def user_passes_role_test(test_func, login_url=None, redirect_field_name='next'):
    """
    Decorator for views that checks that the user passes the given test,
    redirecting to the log-in page if necessary. The test should be a callable
    that takes the user object and returns True if the user passes.
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if test_func(request.user):
                return view_func(request, *args, **kwargs)
            
            # User failed the test
            if not request.user.is_authenticated:
                from django.contrib.auth.views import redirect_to_login
                return redirect_to_login(request.get_full_path(), login_url, redirect_field_name)
            else:
                raise PermissionDenied
        return _wrapped_view
    return decorator


def is_admin(user):
    """Check if user is admin"""
    try:
        return user.userprofile.role == 'admin' and user.userprofile.is_active
    except AttributeError:
        return False


def is_gso_staff(user):
    """Check if user is GSO staff or admin"""
    try:
        return user.userprofile.role in ['admin', 'gso_staff'] and user.userprofile.is_active
    except AttributeError:
        return False


def is_department_user(user):
    """Check if user is department user or above"""
    try:
        return user.userprofile.role in ['admin', 'gso_staff', 'department_user'] and user.userprofile.is_active
    except AttributeError:
        return False


def can_approve_requests(user):
    """Check if user can approve supply requests"""
    return is_gso_staff(user)


def can_manage_inventory(user):
    """Check if user can manage inventory"""
    return is_gso_staff(user)


def can_generate_reports(user):
    """Check if user can generate reports"""
    return is_gso_staff(user)


def can_manage_users(user):
    """Check if user can manage other users"""
    return is_admin(user)