{% extends 'base.html' %}

{% block title %}{{ page_title }} - Smart Supply Management{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'inventory_dashboard' %}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span class="sr-only">Inventory</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'inventory_dashboard' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Inventory</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">Low Stock Alerts</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Low Stock Alerts</h1>
            <p class="mt-1 text-sm text-gray-600">Items that need attention due to low or depleted stock levels</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                {{ total_alerts }} alert{{ total_alerts|pluralize }}
            </span>
        </div>
    </div>

    <!-- Critical Items (Out of Stock) -->
    {% if critical_items %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center mb-4">
                    <svg class="h-6 w-6 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h2 class="text-lg font-medium text-red-900">Critical - Out of Stock</h2>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {{ critical_items.count }} item{{ critical_items.count|pluralize }}
                    </span>
                </div>
                
                <div class="space-y-3">
                    {% for item in critical_items %}
                        <div class="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-red-700">{{ item.item_code|slice:":2"|upper }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-sm font-medium text-red-900">
                                        <a href="{% url 'inventory_item_detail' item.id %}" class="hover:text-red-700">
                                            {{ item.name }}
                                        </a>
                                    </h3>
                                    <div class="mt-1 flex items-center text-sm text-red-700">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                                            {{ item.item_code }}
                                        </span>
                                        <span>{{ item.category.name }}</span>
                                        <span class="mx-2">•</span>
                                        <span>{{ item.unit_of_measure }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <div class="text-lg font-bold text-red-600">0</div>
                                    <div class="text-xs text-red-500">Min: {{ item.minimum_threshold }}</div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{% url 'add_inventory_stock' %}?item={{ item.id }}" 
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700">
                                        Add Stock
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Low Stock Items -->
    {% if low_items %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center mb-4">
                    <svg class="h-6 w-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h2 class="text-lg font-medium text-yellow-900">Low Stock</h2>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {{ low_items.count }} item{{ low_items.count|pluralize }}
                    </span>
                </div>
                
                <div class="space-y-3">
                    {% for item in low_items %}
                        <div class="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-yellow-700">{{ item.item_code|slice:":2"|upper }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-sm font-medium text-yellow-900">
                                        <a href="{% url 'inventory_item_detail' item.id %}" class="hover:text-yellow-700">
                                            {{ item.name }}
                                        </a>
                                    </h3>
                                    <div class="mt-1 flex items-center text-sm text-yellow-700">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                                            {{ item.item_code }}
                                        </span>
                                        <span>{{ item.category.name }}</span>
                                        <span class="mx-2">•</span>
                                        <span>{{ item.unit_of_measure }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <div class="text-lg font-bold text-yellow-600">{{ item.current_stock }}</div>
                                    <div class="text-xs text-yellow-500">Min: {{ item.minimum_threshold }}</div>
                                </div>
                                
                                <!-- Stock Level Progress -->
                                <div class="w-24">
                                    <div class="flex items-center justify-between text-xs text-yellow-600 mb-1">
                                        <span>Stock</span>
                                    </div>
                                    <div class="w-full bg-yellow-200 rounded-full h-2">
                                        {% with stock_percentage=item.current_stock|mul:100|div:item.minimum_threshold %}
                                            <div class="bg-yellow-500 h-2 rounded-full" 
                                                 style="width: {{ stock_percentage|floatformat:0 }}%"></div>
                                        {% endwith %}
                                    </div>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <a href="{% url 'add_inventory_stock' %}?item={{ item.id }}" 
                                       class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700">
                                        Add Stock
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- No Alerts -->
    {% if not critical_items and not low_items %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No Low Stock Alerts</h3>
            <p class="mt-1 text-sm text-gray-500">All inventory items are at or above their minimum thresholds.</p>
            <div class="mt-6">
                <a href="{% url 'inventory_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    Back to Inventory
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}