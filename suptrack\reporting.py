"""
Reporting services for generating comprehensive reports in PDF and CSV formats.
Handles supply request reports, inventory reports, approval workflow reports, and more.
"""

import csv
import io
from datetime import datetime, timedelta
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, F
from django.contrib.auth.models import User
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
