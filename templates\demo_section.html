<!-- Interactive Demo Section -->
<div class="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Try It Yourself
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the power of our Smart Supply Management System with these interactive demos.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- QR Code Generator Demo -->
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <div class="flex items-center mb-6">
                    <div class="bg-primary-100 rounded-lg p-3 mr-4">
                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">QR Code Generator</h3>
                </div>
                
                <div x-data="qrDemo()">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Item Name</label>
                        <input 
                            type="text" 
                            x-model="itemName"
                            placeholder="Enter item name..."
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                        >
                    </div>
                    
                    <button 
                        @click="generateQR()"
                        :disabled="!itemName || loading"
                        class="w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 touch-target"
                    >
                        <span x-show="!loading">Generate QR Code</span>
                        <span x-show="loading" class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                        </span>
                    </button>
                    
                    <div x-show="result" x-transition class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-green-800 font-medium">QR Code Generated!</span>
                        </div>
                        <div class="text-sm text-green-700">
                            <p><strong>Item ID:</strong> <span x-text="result?.data?.item_id"></span></p>
                            <p><strong>Name:</strong> <span x-text="result?.data?.item_name"></span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analytics Dashboard Demo -->
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <div class="flex items-center mb-6">
                    <div class="bg-green-100 rounded-lg p-3 mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">Live Analytics</h3>
                </div>
                
                <div x-data="analyticsDemo()" x-init="loadAnalytics()">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-primary-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-primary-600" x-text="data.total_items || '---'"></div>
                            <div class="text-sm text-primary-700">Total Items</div>
                        </div>
                        <div class="bg-red-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-red-600" x-text="data.low_stock_alerts || '---'"></div>
                            <div class="text-sm text-red-700">Low Stock</div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600" x-text="data.recent_activity || '---'"></div>
                            <div class="text-sm text-blue-700">Recent Activity</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600" x-text="data.efficiency_score ? data.efficiency_score + '%' : '---'"></div>
                            <div class="text-sm text-green-700">Efficiency</div>
                        </div>
                    </div>
                    
                    <button 
                        @click="loadAnalytics()"
                        :disabled="loading"
                        class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 touch-target"
                    >
                        <span x-show="!loading">Refresh Analytics</span>
                        <span x-show="loading">Loading...</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Demo -->
        <div class="mt-12 bg-white rounded-2xl shadow-xl p-8">
            <div class="flex items-center mb-6">
                <div class="bg-purple-100 rounded-lg p-3 mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">Smart Search</h3>
            </div>
            
            <div x-data="searchDemo()">
                <div class="mb-6">
                    <div class="relative">
                        <input 
                            type="text" 
                            x-model="query"
                            @input.debounce.300ms="search()"
                            placeholder="Search inventory items..."
                            class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                        >
                        <svg class="absolute left-4 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
                
                <div x-show="results.length > 0" x-transition class="space-y-3">
                    <template x-for="item in results" :key="item.id">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div>
                                <h4 class="font-semibold text-gray-900" x-text="item.name"></h4>
                                <p class="text-sm text-gray-600" x-text="item.category"></p>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold" x-text="item.stock + ' units'"></div>
                                <div class="text-sm" 
                                     :class="{
                                         'text-green-600': item.status === 'In Stock',
                                         'text-yellow-600': item.status === 'Low Stock',
                                         'text-red-600': item.status === 'Critical'
                                     }"
                                     x-text="item.status">
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                
                <div x-show="query && results.length === 0" x-transition class="text-center py-8 text-gray-500">
                    No items found matching your search.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function qrDemo() {
    return {
        itemName: '',
        loading: false,
        result: null,
        
        async generateQR() {
            if (!this.itemName) return;
            
            this.loading = true;
            this.result = null;
            
            try {
                const response = await fetch('/demo/qr-generate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        item_name: this.itemName
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.result = data;
                    showNotification('QR code generated successfully!', 'success');
                } else {
                    showNotification(data.message || 'Failed to generate QR code', 'error');
                }
            } catch (error) {
                showNotification('Network error occurred', 'error');
            } finally {
                this.loading = false;
            }
        }
    }
}

function analyticsDemo() {
    return {
        data: {},
        loading: false,
        
        async loadAnalytics() {
            this.loading = true;
            
            try {
                const response = await fetch('/demo/analytics/');
                const data = await response.json();
                this.data = data;
            } catch (error) {
                showNotification('Failed to load analytics', 'error');
            } finally {
                this.loading = false;
            }
        }
    }
}

function searchDemo() {
    return {
        query: '',
        results: [],
        
        async search() {
            if (!this.query.trim()) {
                this.results = [];
                return;
            }
            
            try {
                const response = await fetch(`/demo/search/?q=${encodeURIComponent(this.query)}`);
                const data = await response.json();
                this.results = data.items || [];
            } catch (error) {
                showNotification('Search failed', 'error');
            }
        }
    }
}
</script>