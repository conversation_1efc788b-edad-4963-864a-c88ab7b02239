{% extends 'base.html' %}
{% load static %}

{% block title %}Profile - Smart Supply Management{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-900">User Profile</h1>
                <div class="flex space-x-3">
                    <a href="{% url 'change_password' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-3m-3 0h3m0 0V9a2 2 0 00-2-2m0 0a2 2 0 00-2-2V5a2 2 0 012-2h3"></path>
                        </svg>
                        Change Password
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Profile Information -->
                <div class="lg:col-span-2">
                    <form method="post" class="space-y-6">
                        {% csrf_token %}
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div>
                                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div>
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Address</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.department.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone Number</label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.phone_number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Profile Summary -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Username</dt>
                            <dd class="text-sm text-gray-900">{{ user.username }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Role</dt>
                            <dd class="text-sm text-gray-900">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if profile.role == 'admin' %}bg-purple-100 text-purple-800
                                    {% elif profile.role == 'gso_staff' %}bg-blue-100 text-blue-800
                                    {% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ profile.get_role_display }}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                            <dd class="text-sm text-gray-900">{{ user.date_joined|date:"F d, Y" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Login</dt>
                            <dd class="text-sm text-gray-900">
                                {% if user.last_login %}
                                    {{ user.last_login|date:"F d, Y g:i A" }}
                                {% else %}
                                    Never
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                            <dd class="text-sm text-gray-900">
                                {% if profile.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}