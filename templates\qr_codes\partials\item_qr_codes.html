{% if qr_codes %}
    <div class="space-y-4">
        {% for qr_code in qr_codes %}
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center space-x-4">
                    <!-- QR Code Image -->
                    <div class="flex-shrink-0">
                        {% if qr_code.qr_image %}
                            <img src="{{ qr_code.qr_image.url }}" 
                                 alt="QR Code" 
                                 class="h-16 w-16 border border-gray-300 rounded">
                        {% else %}
                            <div class="h-16 w-16 bg-gray-100 border border-gray-300 rounded flex items-center justify-center">
                                <i class="fas fa-qrcode text-gray-400"></i>
                            </div>
                        {% endif %}
                    </div>

                    <!-- QR Code Details -->
                    <div class="flex-1">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Code ID</label>
                                <p class="text-sm font-mono text-gray-900">{{ qr_code.code_id }}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Request</label>
                                <p class="text-sm font-mono text-gray-900">
                                    <a href="{% url 'request_detail' qr_code.request_item.request.request_id %}" 
                                       class="text-blue-600 hover:text-blue-800">
                                        {{ qr_code.request_item.request.request_id }}
                                    </a>
                                </p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Department</label>
                                <p class="text-sm text-gray-900">{{ qr_code.request_item.request.department }}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Status</label>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if qr_code.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                    {% if qr_code.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Generated</label>
                                <p class="text-sm text-gray-900">{{ qr_code.generated_date|date:"M d, Y" }}</p>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-500">Scans</label>
                                <p class="text-sm text-gray-900">{{ qr_code.scanned_count }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex-shrink-0">
                        <div class="flex flex-col space-y-1">
                            <a href="{% url 'qr_code_detail' qr_code.code_id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                View
                            </a>
                            <a href="{% url 'qr_code_print_label' qr_code.code_id %}" 
                               class="text-green-600 hover:text-green-800 text-sm">
                                Print
                            </a>
                            {% if user.userprofile.role in 'gso_staff,admin' %}
                                <a href="{% url 'regenerate_qr_code' qr_code.code_id %}" 
                                   class="text-orange-600 hover:text-orange-800 text-sm">
                                    Regenerate
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-8">
        <i class="fas fa-qrcode text-gray-400 text-4xl mb-4"></i>
        <p class="text-gray-500">No QR codes found for this item</p>
    </div>
{% endif %}