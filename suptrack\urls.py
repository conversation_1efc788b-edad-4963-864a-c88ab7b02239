from django.urls import path
from .views import (
    home, test_foundation, test_htmx, 
    demo_qr_generate, demo_analytics, demo_search,
    # Supply request views
    create_supply_request, quick_request, request_list, request_detail,
    get_item_details, add_request_item, validate_stock, cancel_request, request_dashboard,
    # Approval workflow views
    pending_requests_dashboard, review_request, approve_request, reject_request,
    release_items, release_confirmation, approved_requests_list,
    # Inventory management views
    inventory_dashboard, inventory_item_detail, add_inventory_stock, adjust_inventory_stock,
    low_stock_alerts, inventory_search, get_inventory_status, get_item_stock_info,
    # QR Code management views
    qr_code_management, qr_code_detail, qr_code_print_label, regenerate_qr_code,
    scan_qr_code, validate_qr_data, qr_scanner_interface, 
    get_qr_codes_for_request, get_qr_codes_for_item,
    # Mobile QR Scanner views
    process_item_issuance, process_item_return, process_inventory_audit, get_scan_history
)
from .auth_views import (
    register_view, login_view, logout_view,
    CustomPasswordResetView, CustomPasswordResetConfirmView,
    profile_view, user_management_view, toggle_user_status,
    user_detail_view, change_password_view,
    password_reset_done_view, password_reset_complete_view
)

urlpatterns = [
    path('', home, name='home'),
    path('test-foundation/', test_foundation, name='test_foundation'),
    path('test-htmx/', test_htmx, name='test_htmx'),
    
    # Authentication URLs
    path('register/', register_view, name='register'),
    path('login/', login_view, name='login'),
    path('logout/', logout_view, name='logout'),
    path('profile/', profile_view, name='profile'),
    path('change-password/', change_password_view, name='change_password'),
    
    # Password reset URLs
    path('password-reset/', CustomPasswordResetView.as_view(), name='password_reset'),
    path('password-reset/done/', password_reset_done_view, name='password_reset_done'),
    path('password-reset/confirm/<uidb64>/<token>/', CustomPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('password-reset/complete/', password_reset_complete_view, name='password_reset_complete'),
    
    # User management URLs (Admin only)
    path('admin/users/', user_management_view, name='user_management'),
    path('admin/users/<int:user_id>/', user_detail_view, name='user_detail'),
    path('admin/users/<int:user_id>/toggle-status/', toggle_user_status, name='toggle_user_status'),
    
    # Supply request URLs
    path('requests/', request_list, name='request_list'),
    path('requests/dashboard/', request_dashboard, name='request_dashboard'),
    path('requests/create/', create_supply_request, name='create_request'),
    path('requests/quick/', quick_request, name='quick_request'),
    path('requests/<str:request_id>/', request_detail, name='request_detail'),
    path('requests/<str:request_id>/cancel/', cancel_request, name='cancel_request'),
    
    # Approval workflow URLs
    path('requests/pending/dashboard/', pending_requests_dashboard, name='pending_requests_dashboard'),
    path('requests/<str:request_id>/review/', review_request, name='review_request'),
    path('requests/<str:request_id>/approve/', approve_request, name='approve_request'),
    path('requests/<str:request_id>/reject/', reject_request, name='reject_request'),
    path('requests/<str:request_id>/release/', release_items, name='release_items'),
    path('requests/<str:request_id>/release/confirmation/', release_confirmation, name='release_confirmation'),
    path('requests/approved/list/', approved_requests_list, name='approved_requests_list'),
    
    # HTMX endpoints for requests
    path('requests/item-details/', get_item_details, name='get_item_details'),
    path('requests/add-item/', add_request_item, name='add_request_item'),
    path('requests/validate-stock/', validate_stock, name='validate_stock'),
    path('requests/list/', request_list, name='request_list_htmx'),
    
    # Inventory management URLs
    path('inventory/', inventory_dashboard, name='inventory_dashboard'),
    path('inventory/item/<int:item_id>/', inventory_item_detail, name='inventory_item_detail'),
    path('inventory/add-stock/', add_inventory_stock, name='add_inventory_stock'),
    path('inventory/adjust-stock/', adjust_inventory_stock, name='adjust_inventory_stock'),
    path('inventory/low-stock-alerts/', low_stock_alerts, name='low_stock_alerts'),
    
    # HTMX endpoints for inventory
    path('inventory/search/', inventory_search, name='inventory_search'),
    path('inventory/status/', get_inventory_status, name='get_inventory_status'),
    path('inventory/item-stock-info/', get_item_stock_info, name='get_item_stock_info'),
    
    # QR Code management URLs
    path('qr-codes/', qr_code_management, name='qr_code_management'),
    path('qr-codes/<str:code_id>/', qr_code_detail, name='qr_code_detail'),
    path('qr-codes/<str:code_id>/print/', qr_code_print_label, name='qr_code_print_label'),
    path('qr-codes/<str:code_id>/regenerate/', regenerate_qr_code, name='regenerate_qr_code'),
    path('qr-codes/scanner/', qr_scanner_interface, name='qr_scanner_interface'),
    
    # QR Code API endpoints
    path('api/qr-codes/scan/', scan_qr_code, name='scan_qr_code'),
    path('api/qr-codes/validate/', validate_qr_data, name='validate_qr_data'),
    path('api/qr-codes/request/<str:request_id>/', get_qr_codes_for_request, name='get_qr_codes_for_request'),
    path('api/qr-codes/item/<int:item_id>/', get_qr_codes_for_item, name='get_qr_codes_for_item'),
    
    # Mobile QR Scanner API endpoints
    path('api/qr-scanner/process-issuance/', process_item_issuance, name='process_item_issuance'),
    path('api/qr-scanner/process-return/', process_item_return, name='process_item_return'),
    path('api/qr-scanner/process-audit/', process_inventory_audit, name='process_inventory_audit'),
    path('api/qr-scanner/scan-history/', get_scan_history, name='get_scan_history'),
    
    # Demo endpoints
    path('demo/qr-generate/', demo_qr_generate, name='demo_qr_generate'),
    path('demo/analytics/', demo_analytics, name='demo_analytics'),
    path('demo/search/', demo_search, name='demo_search'),
]
