<!-- Request Details Section -->
<div class="border-b border-gray-200 pb-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Request Details</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="{{ form.justification.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.justification.label }}
            </label>
            {{ form.justification }}
            {% if form.justification.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.justification.errors.0 }}
                </div>
            {% endif %}
        </div>
        
        <div>
            <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.required_date.label }}
            </label>
            {{ form.required_date }}
            {% if form.required_date.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.required_date.errors.0 }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <div class="mt-4">
        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            {{ form.notes.label }}
        </label>
        {{ form.notes }}
        {% if form.notes.errors %}
            <div class="mt-1 text-sm text-red-600">
                {{ form.notes.errors.0 }}
            </div>
        {% endif %}
    </div>
</div>

<!-- Request Items Section -->
<div class="pt-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Request Items</h2>
        <button type="button" 
                hx-get="{% url 'add_request_item' %}"
                hx-target="#request-items"
                hx-swap="beforeend"
                hx-vals="js:{total_forms: document.querySelector('input[name=items-TOTAL_FORMS]').value}"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                onclick="updateFormCount()"
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Item
        </button>
    </div>
    
    <div id="request-items" class="space-y-4">
        {{ formset.management_form }}
        {% for form in formset %}
            {% include 'requests/partials/request_item_form.html' with form=form form_index=forloop.counter0 %}
        {% endfor %}
    </div>
    
    {% if formset.non_form_errors %}
        <div class="mt-4 text-sm text-red-600">
            {{ formset.non_form_errors }}
        </div>
    {% endif %}
</div>

<!-- Submit Section -->
<div class="pt-6 border-t border-gray-200">
    <div class="flex justify-end space-x-3">
        <a href="{% url 'request_list' %}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Cancel
        </a>
        <button type="submit" 
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <span class="htmx-indicator">
                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </span>
            Create Request
        </button>
    </div>
</div>