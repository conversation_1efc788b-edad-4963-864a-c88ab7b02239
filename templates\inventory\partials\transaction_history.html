<div class="overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performed By
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notes
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for transaction in transactions_page %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                            <div class="font-medium">{{ transaction.timestamp|date:"M d, Y" }}</div>
                            <div class="text-gray-500">{{ transaction.timestamp|time:"g:i A" }}</div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if transaction.transaction_type == 'in' %}bg-green-100 text-green-800
                            {% elif transaction.transaction_type == 'out' %}bg-red-100 text-red-800
                            {% elif transaction.transaction_type == 'adjustment' %}bg-yellow-100 text-yellow-800
                            {% elif transaction.transaction_type == 'return' %}bg-blue-100 text-blue-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ transaction.get_transaction_type_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium
                        {% if transaction.quantity > 0 %}text-green-600{% else %}text-red-600{% endif %}">
                        {% if transaction.quantity > 0 %}+{% endif %}{{ transaction.quantity }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ transaction.performed_by.get_full_name|default:transaction.performed_by.username }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {% if transaction.reference_document %}
                            {% if transaction.request_item %}
                                <a href="{% url 'request_detail' transaction.request_item.request.request_id %}" 
                                   class="text-primary-600 hover:text-primary-500">
                                    {{ transaction.reference_document }}
                                </a>
                            {% else %}
                                {{ transaction.reference_document }}
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                        {% if transaction.notes %}
                            <div class="max-w-xs truncate" title="{{ transaction.notes }}">
                                {{ transaction.notes }}
                            </div>
                        {% else %}
                            -
                        {% endif %}
                    </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="mt-2">No transactions found</p>
                        {% if filters.transaction_type or filters.date_from or filters.date_to %}
                            <p class="text-sm text-gray-400 mt-1">Try adjusting your filters</p>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Pagination -->
    {% if transactions_page.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if transactions_page.has_previous %}
                    <a href="?page={{ transactions_page.previous_page_number }}{% if filters.transaction_type %}&transaction_type={{ filters.transaction_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if transactions_page.has_next %}
                    <a href="?page={{ transactions_page.next_page_number }}{% if filters.transaction_type %}&transaction_type={{ filters.transaction_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ transactions_page.start_index }}</span>
                        to
                        <span class="font-medium">{{ transactions_page.end_index }}</span>
                        of
                        <span class="font-medium">{{ transactions_page.paginator.count }}</span>
                        transactions
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if transactions_page.has_previous %}
                            <a href="?page={{ transactions_page.previous_page_number }}{% if filters.transaction_type %}&transaction_type={{ filters.transaction_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in transactions_page.paginator.page_range %}
                            {% if transactions_page.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                    {{ num }}
                                </span>
                            {% elif num > transactions_page.number|add:'-3' and num < transactions_page.number|add:'3' %}
                                <a href="?page={{ num }}{% if filters.transaction_type %}&transaction_type={{ filters.transaction_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if transactions_page.has_next %}
                            <a href="?page={{ transactions_page.next_page_number }}{% if filters.transaction_type %}&transaction_type={{ filters.transaction_type }}{% endif %}{% if filters.date_from %}&date_from={{ filters.date_from }}{% endif %}{% if filters.date_to %}&date_to={{ filters.date_to }}{% endif %}" 
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}
</div>