{% extends 'base.html' %}
{% load static %}

{% block title %}Change Password - Smart Supply Management{% endblock %}

{% block content %}
<div class="max-w-md mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900">Change Password</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Enter your current password and choose a new secure password.
                </p>
            </div>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
                    <input type="password" name="current_password" id="current_password" required
                           class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter your current password">
                </div>

                <div>
                    <label for="new_password1" class="block text-sm font-medium text-gray-700">New Password</label>
                    <input type="password" name="new_password1" id="new_password1" required
                           class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter your new password">
                    <p class="mt-1 text-xs text-gray-500">Password must be at least 8 characters long.</p>
                </div>

                <div>
                    <label for="new_password2" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                    <input type="password" name="new_password2" id="new_password2" required
                           class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Confirm your new password">
                </div>

                <div class="flex justify-between">
                    <a href="{% url 'profile' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-3m-3 0h3m0 0V9a2 2 0 00-2-2m0 0a2 2 0 00-2-2V5a2 2 0 012-2h3"></path>
                        </svg>
                        Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}