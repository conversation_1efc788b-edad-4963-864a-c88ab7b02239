"""
Custom middleware for Smart Supply Management System
"""
import logging
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponseForbidden
from django.template.loader import render_to_string

logger = logging.getLogger(__name__)


class RoleVerificationMiddleware(MiddlewareMixin):
    """
    Middleware for automatic role verification and session management.
    
    This middleware:
    1. Verifies user profile exists and is active
    2. Handles inactive user sessions
    3. Logs security events
    4. Provides role context to templates
    """
    
    # URLs that don't require role verification
    EXEMPT_URLS = [
        '/login/',
        '/logout/',
        '/register/',
        '/password-reset/',
        '/admin/',  # Django admin
        '/static/',
        '/media/',
        '/test-foundation/',
        '/test-htmx/',
    ]
    
    def process_request(self, request):
        """Process incoming requests for role verification"""
        
        # Skip verification for exempt URLs
        if any(request.path.startswith(url) for url in self.EXEMPT_URLS):
            return None
        
        # Skip verification for unauthenticated users
        if not request.user.is_authenticated:
            return None
        
        try:
            # Check if user profile exists
            profile = request.user.userprofile
            
            # Check if user account is active
            if not profile.is_active:
                logger.warning(f"Inactive user attempted access: {request.user.username}")
                messages.error(
                    request, 
                    "Your account has been deactivated. Please contact an administrator."
                )
                # Log out the user and redirect to login
                from django.contrib.auth import logout
                logout(request)
                return redirect('login')
            
            # Add role information to request for easy access in views
            request.user_role = profile.role
            request.user_department = profile.department
            
        except AttributeError:
            # User profile doesn't exist
            logger.error(f"User without profile attempted access: {request.user.username}")
            messages.error(
                request,
                "User profile not found. Please contact an administrator."
            )
            from django.contrib.auth import logout
            logout(request)
            return redirect('login')
        
        return None
    
    def process_response(self, request, response):
        """Process responses to add security headers"""
        
        # Add security headers for authenticated users
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Add cache control headers for sensitive pages
            if not any(request.path.startswith(url) for url in ['/static/', '/media/']):
                response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response['Pragma'] = 'no-cache'
                response['Expires'] = '0'
        
        return response


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    Middleware for enhanced session security.
    
    This middleware:
    1. Validates session integrity
    2. Handles session timeout warnings
    3. Logs session activities
    """
    
    def process_request(self, request):
        """Process requests for session security"""
        
        if request.user.is_authenticated:
            # Check for session hijacking indicators
            current_ip = self.get_client_ip(request)
            current_user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # Store/check session fingerprint
            session_ip = request.session.get('session_ip')
            session_user_agent = request.session.get('session_user_agent')
            
            if session_ip is None:
                # First request in session, store fingerprint
                request.session['session_ip'] = current_ip
                request.session['session_user_agent'] = current_user_agent
                logger.info(f"New session started for user: {request.user.username} from IP: {current_ip}")
            else:
                # Check for session anomalies (basic check)
                if session_ip != current_ip:
                    logger.warning(
                        f"IP change detected for user {request.user.username}: "
                        f"{session_ip} -> {current_ip}"
                    )
                    # In production, you might want to force re-authentication here
                
                # Update last activity
                request.session['last_activity'] = request.session.get('last_activity', 0)
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AuditLogMiddleware(MiddlewareMixin):
    """
    Middleware for audit logging of sensitive operations.
    
    This middleware logs:
    1. Authentication events
    2. Permission changes
    3. Data modifications
    4. Administrative actions
    """
    
    # URLs that should be audited
    AUDIT_URLS = [
        '/admin/users/',
        '/profile/',
        '/change-password/',
        '/register/',
    ]
    
    # HTTP methods that should be audited
    AUDIT_METHODS = ['POST', 'PUT', 'PATCH', 'DELETE']
    
    def process_request(self, request):
        """Log audit events for sensitive operations"""
        
        # Only audit specific URLs and methods
        should_audit = (
            any(request.path.startswith(url) for url in self.AUDIT_URLS) and
            request.method in self.AUDIT_METHODS
        )
        
        if should_audit and request.user.is_authenticated:
            logger.info(
                f"AUDIT: User {request.user.username} "
                f"({getattr(request, 'user_role', 'unknown')}) "
                f"performed {request.method} on {request.path} "
                f"from IP {self.get_client_ip(request)}"
            )
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware for custom error handling and user-friendly error pages.
    """
    
    def process_exception(self, request, exception):
        """Handle exceptions with user-friendly messages"""
        
        # Log the exception
        logger.error(
            f"Exception for user {getattr(request.user, 'username', 'anonymous')}: "
            f"{type(exception).__name__}: {str(exception)}",
            exc_info=True
        )
        
        # Don't handle exceptions in debug mode
        from django.conf import settings
        if settings.DEBUG:
            return None
        
        # Handle specific exception types
        if isinstance(exception, PermissionError):
            messages.error(request, "You don't have permission to perform this action.")
        elif isinstance(exception, ValueError):
            messages.error(request, "Invalid data provided. Please check your input.")
        else:
            messages.error(request, "An unexpected error occurred. Please try again.")
        
        return None