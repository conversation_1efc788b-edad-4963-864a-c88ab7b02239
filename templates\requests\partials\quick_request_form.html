<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Supply Item -->
    <div class="md:col-span-2">
        <label for="{{ form.supply_item.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Supply Item
        </label>
        {{ form.supply_item }}
        {% if form.supply_item.errors %}
            <div class="mt-1 text-sm text-red-600">
                {{ form.supply_item.errors.0 }}
            </div>
        {% endif %}
    </div>
    
    <!-- Quantity -->
    <div>
        <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Quantity
        </label>
        {{ form.quantity }}
        {% if form.quantity.errors %}
            <div class="mt-1 text-sm text-red-600">
                {{ form.quantity.errors.0 }}
            </div>
        {% endif %}
    </div>
    
    <!-- Required Date -->
    <div>
        <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Required Date
        </label>
        {{ form.required_date }}
        {% if form.required_date.errors %}
            <div class="mt-1 text-sm text-red-600">
                {{ form.required_date.errors.0 }}
            </div>
        {% endif %}
    </div>
    
    <!-- Justification -->
    <div class="md:col-span-2">
        <label for="{{ form.justification.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Justification
        </label>
        {{ form.justification }}
        {% if form.justification.errors %}
            <div class="mt-1 text-sm text-red-600">
                {{ form.justification.errors.0 }}
            </div>
        {% endif %}
    </div>
</div>