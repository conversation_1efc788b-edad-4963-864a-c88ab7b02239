from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm, PasswordResetForm, SetPasswordForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.forms import formset_factory
from django.utils import timezone
from datetime import date, timedelta
from .models import UserProfile, SupplyRequest, RequestItem, SupplyItem, Category


class CustomUserRegistrationForm(UserCreationForm):
    """Custom user registration form with role assignment"""
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your email address'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your first name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your last name'
        })
    )
    role = forms.ChoiceField(
        choices=UserProfile.ROLE_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    department = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your department'
        })
    )
    phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your phone number (optional)'
        })
    )

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Choose a username'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes to password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Confirm your password'
        })

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        
        if commit:
            user.save()
            # Create UserProfile
            UserProfile.objects.create(
                user=user,
                role=self.cleaned_data['role'],
                department=self.cleaned_data['department'],
                phone_number=self.cleaned_data.get('phone_number', '')
            )
        return user


class CustomLoginForm(AuthenticationForm):
    """Custom login form with enhanced styling"""
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your username',
            'autofocus': True
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your password'
        })
    )
    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        if self.user_cache:
            # Check if user profile exists and is active
            try:
                profile = self.user_cache.userprofile
                if not profile.is_active:
                    raise ValidationError("Your account has been deactivated. Please contact an administrator.")
            except UserProfile.DoesNotExist:
                raise ValidationError("User profile not found. Please contact an administrator.")
        return cleaned_data


class CustomPasswordResetForm(PasswordResetForm):
    """Custom password reset form"""
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter your email address'
        })
    )


class CustomSetPasswordForm(SetPasswordForm):
    """Custom set password form"""
    new_password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter new password'
        })
    )
    new_password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Confirm new password'
        })
    )


class UserProfileForm(forms.ModelForm):
    """Form for updating user profile information"""
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )

    class Meta:
        model = UserProfile
        fields = ['department', 'phone_number']
        widgets = {
            'department': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        if self.user:
            self.fields['first_name'].initial = self.user.first_name
            self.fields['last_name'].initial = self.user.last_name
            self.fields['email'].initial = self.user.email

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if self.user and User.objects.filter(email=email).exclude(pk=self.user.pk).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    def save(self, commit=True):
        profile = super().save(commit=False)
        if self.user:
            self.user.first_name = self.cleaned_data['first_name']
            self.user.last_name = self.cleaned_data['last_name']
            self.user.email = self.cleaned_data['email']
            if commit:
                self.user.save()
        if commit:
            profile.save()
        return profile


class SupplyRequestForm(forms.ModelForm):
    """Form for creating supply requests"""
    
    class Meta:
        model = SupplyRequest
        fields = ['justification', 'required_date', 'notes']
        widgets = {
            'justification': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Provide justification for this supply request...',
                'rows': 4,
                'required': True
            }),
            'required_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'type': 'date',
                'required': True
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Additional notes (optional)...',
                'rows': 2
            })
        }
        labels = {
            'justification': 'Justification for Request',
            'required_date': 'Required Date',
            'notes': 'Additional Notes'
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set minimum date to tomorrow
        tomorrow = date.today() + timedelta(days=1)
        self.fields['required_date'].widget.attrs['min'] = tomorrow.strftime('%Y-%m-%d')

    def clean_required_date(self):
        required_date = self.cleaned_data.get('required_date')
        if required_date and required_date <= date.today():
            raise ValidationError("Required date must be at least tomorrow.")
        return required_date

    def clean_justification(self):
        justification = self.cleaned_data.get('justification', '').strip()
        if len(justification) < 10:
            raise ValidationError("Justification must be at least 10 characters long.")
        return justification

    def save(self, commit=True):
        request = super().save(commit=False)
        if self.user:
            request.requester = self.user
            request.department = self.user.userprofile.department
        if commit:
            request.save()
        return request


class RequestItemForm(forms.ModelForm):
    """Form for individual request items"""
    
    supply_item = forms.ModelChoiceField(
        queryset=SupplyItem.objects.filter(is_active=True).select_related('category'),
        empty_label="Select an item...",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'hx-get': '/requests/item-details/',
            'hx-target': '#item-details',
            'hx-trigger': 'change'
        })
    )
    
    quantity_requested = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Enter quantity',
            'min': '1'
        })
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Item-specific notes (optional)'
        })
    )

    class Meta:
        model = RequestItem
        fields = ['supply_item', 'quantity_requested', 'notes']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Group items by category for better UX
        self.fields['supply_item'].queryset = self.fields['supply_item'].queryset.order_by('category__name', 'name')

    def clean_quantity_requested(self):
        quantity = self.cleaned_data.get('quantity_requested')
        supply_item = self.cleaned_data.get('supply_item')
        
        if quantity and supply_item:
            if quantity > supply_item.current_stock:
                raise ValidationError(
                    f"Requested quantity ({quantity}) exceeds available stock ({supply_item.current_stock})"
                )
        return quantity


# Create formset for multiple request items
RequestItemFormSet = formset_factory(
    RequestItemForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class SupplyRequestFilterForm(forms.Form):
    """Form for filtering supply requests"""
    
    STATUS_CHOICES = [('', 'All Statuses')] + SupplyRequest.STATUS_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'hx-get': '/requests/list/',
            'hx-target': '#request-list',
            'hx-trigger': 'change'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date',
            'hx-get': '/requests/list/',
            'hx-target': '#request-list',
            'hx-trigger': 'change'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date',
            'hx-get': '/requests/list/',
            'hx-target': '#request-list',
            'hx-trigger': 'change'
        })
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Search by request ID or justification...',
            'hx-get': '/requests/list/',
            'hx-target': '#request-list',
            'hx-trigger': 'keyup changed delay:500ms'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError("Start date cannot be after end date.")
        
        return cleaned_data


class QuickRequestForm(forms.Form):
    """Simplified form for quick single-item requests"""
    
    supply_item = forms.ModelChoiceField(
        queryset=SupplyItem.objects.filter(is_active=True, current_stock__gt=0).select_related('category'),
        empty_label="Select an item...",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        })
    )
    
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Quantity',
            'min': '1'
        })
    )
    
    justification = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Brief justification...',
            'rows': 2
        })
    )
    
    required_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'type': 'date'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set minimum date to tomorrow
        tomorrow = date.today() + timedelta(days=1)
        self.fields['required_date'].widget.attrs['min'] = tomorrow.strftime('%Y-%m-%d')
        self.fields['required_date'].initial = tomorrow

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        supply_item = self.cleaned_data.get('supply_item')
        
        if quantity and supply_item:
            if quantity > supply_item.current_stock:
                raise ValidationError(
                    f"Requested quantity ({quantity}) exceeds available stock ({supply_item.current_stock})"
                )
        return quantity

    def clean_required_date(self):
        required_date = self.cleaned_data.get('required_date')
        if required_date and required_date <= date.today():
            raise ValidationError("Required date must be at least tomorrow.")
        return required_date


class RequestApprovalForm(forms.Form):
    """Form for approving supply requests with quantity adjustments"""
    
    def __init__(self, *args, **kwargs):
        self.request_items = kwargs.pop('request_items', [])
        super().__init__(*args, **kwargs)
        
        # Create fields for each request item
        for item in self.request_items:
            field_name = f'quantity_approved_{item.id}'
            self.fields[field_name] = forms.IntegerField(
                label=f'{item.supply_item.name} (Requested: {item.quantity_requested})',
                initial=item.quantity_requested,
                min_value=0,
                max_value=min(item.quantity_requested, item.supply_item.current_stock),
                widget=forms.NumberInput(attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                    'data-max-stock': item.supply_item.current_stock,
                    'data-requested': item.quantity_requested
                }),
                help_text=f'Available stock: {item.supply_item.current_stock} {item.supply_item.unit_of_measure}'
            )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Approval notes (optional)...',
            'rows': 3
        }),
        label='Approval Notes'
    )

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate each item's approved quantity
        for item in self.request_items:
            field_name = f'quantity_approved_{item.id}'
            approved_qty = cleaned_data.get(field_name)
            
            if approved_qty is not None:
                if approved_qty > item.supply_item.current_stock:
                    self.add_error(field_name, 
                        f'Approved quantity ({approved_qty}) exceeds available stock ({item.supply_item.current_stock})')
                
                if approved_qty > item.quantity_requested:
                    self.add_error(field_name, 
                        f'Approved quantity ({approved_qty}) cannot exceed requested quantity ({item.quantity_requested})')
        
        return cleaned_data


class RequestRejectionForm(forms.Form):
    """Form for rejecting supply requests"""
    
    rejection_reason = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500',
            'placeholder': 'Please provide a reason for rejection...',
            'rows': 4,
            'required': True
        }),
        label='Rejection Reason',
        help_text='This reason will be visible to the requester.'
    )
    
    notify_requester = forms.BooleanField(
        initial=True,
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded'
        }),
        label='Send notification to requester'
    )

    def clean_rejection_reason(self):
        reason = self.cleaned_data.get('rejection_reason', '').strip()
        if len(reason) < 10:
            raise ValidationError("Rejection reason must be at least 10 characters long.")
        return reason


class ItemReleaseForm(forms.Form):
    """Form for releasing approved items with QR code generation"""
    
    def __init__(self, *args, **kwargs):
        self.approved_items = kwargs.pop('approved_items', [])
        super().__init__(*args, **kwargs)
        
        # Create fields for each approved item
        for item in self.approved_items:
            if item.quantity_approved and item.quantity_approved > 0:
                field_name = f'release_quantity_{item.id}'
                self.fields[field_name] = forms.IntegerField(
                    label=f'{item.supply_item.name}',
                    initial=item.quantity_approved,
                    min_value=1,
                    max_value=item.quantity_approved,
                    widget=forms.NumberInput(attrs={
                        'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                        'data-approved': item.quantity_approved
                    }),
                    help_text=f'Approved quantity: {item.quantity_approved} {item.supply_item.unit_of_measure}'
                )
                
                # Checkbox for generating QR code
                qr_field_name = f'generate_qr_{item.id}'
                self.fields[qr_field_name] = forms.BooleanField(
                    initial=True,
                    required=False,
                    widget=forms.CheckboxInput(attrs={
                        'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                    }),
                    label=f'Generate QR code for {item.supply_item.name}'
                )
    
    release_notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Release notes (optional)...',
            'rows': 2
        }),
        label='Release Notes'
    )

    def clean(self):
        cleaned_data = super().clean()
        
        # Validate each item's release quantity
        for item in self.approved_items:
            if item.quantity_approved and item.quantity_approved > 0:
                field_name = f'release_quantity_{item.id}'
                release_qty = cleaned_data.get(field_name)
                
                if release_qty is not None:
                    if release_qty > item.quantity_approved:
                        self.add_error(field_name, 
                            f'Release quantity ({release_qty}) cannot exceed approved quantity ({item.quantity_approved})')
        
        return cleaned_data