#!/usr/bin/env python
"""
Test script to verify supply request management system functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from suptrack.models import UserProfile, Category, SupplyItem, SupplyRequest, RequestItem
from django.utils import timezone
from datetime import date, timedelta

def create_test_data():
    """Create test data for supply request system"""
    print("Creating test data...")
    
    # Create test users if they don't exist
    if not User.objects.filter(username='testuser').exists():
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        UserProfile.objects.create(
            user=user,
            role='department_user',
            department='IT Department',
            phone_number='************'
        )
        print("✓ Created test user")
    
    if not User.objects.filter(username='gsostaff').exists():
        gso_user = User.objects.create_user(
            username='gsostaff',
            email='<EMAIL>',
            password='testpass123',
            first_name='GSO',
            last_name='Staff'
        )
        UserProfile.objects.create(
            user=gso_user,
            role='gso_staff',
            department='General Services Office',
            phone_number='************'
        )
        print("✓ Created GSO staff user")
    
    # Create categories
    categories_data = [
        {'name': 'Office Supplies', 'description': 'General office supplies and stationery'},
        {'name': 'Electronics', 'description': 'Electronic equipment and accessories'},
        {'name': 'Cleaning Supplies', 'description': 'Cleaning materials and equipment'},
    ]
    
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        if created:
            print(f"✓ Created category: {category.name}")
    
    # Create supply items
    office_category = Category.objects.get(name='Office Supplies')
    electronics_category = Category.objects.get(name='Electronics')
    cleaning_category = Category.objects.get(name='Cleaning Supplies')
    
    items_data = [
        {
            'item_code': 'PEN-001',
            'name': 'Ballpoint Pens (Blue)',
            'description': 'Blue ballpoint pens, pack of 12',
            'category': office_category,
            'unit_of_measure': 'pack',
            'current_stock': 50,
            'minimum_threshold': 10,
            'unit_cost': 5.99,
            'supplier': 'Office Depot'
        },
        {
            'item_code': 'PAP-001',
            'name': 'A4 Paper',
            'description': 'White A4 paper, 500 sheets per ream',
            'category': office_category,
            'unit_of_measure': 'ream',
            'current_stock': 25,
            'minimum_threshold': 5,
            'unit_cost': 8.50,
            'supplier': 'Paper Plus'
        },
        {
            'item_code': 'USB-001',
            'name': 'USB Flash Drive 32GB',
            'description': '32GB USB 3.0 flash drive',
            'category': electronics_category,
            'unit_of_measure': 'piece',
            'current_stock': 15,
            'minimum_threshold': 5,
            'unit_cost': 12.99,
            'supplier': 'Tech Store'
        },
        {
            'item_code': 'CLN-001',
            'name': 'All-Purpose Cleaner',
            'description': 'Multi-surface cleaning spray, 500ml',
            'category': cleaning_category,
            'unit_of_measure': 'bottle',
            'current_stock': 8,
            'minimum_threshold': 10,
            'unit_cost': 3.75,
            'supplier': 'Cleaning Co'
        }
    ]
    
    for item_data in items_data:
        item, created = SupplyItem.objects.get_or_create(
            item_code=item_data['item_code'],
            defaults=item_data
        )
        if created:
            print(f"✓ Created supply item: {item.name}")
    
    print("Test data creation completed!")

def test_supply_request_creation():
    """Test creating a supply request"""
    print("\nTesting supply request creation...")
    
    user = User.objects.get(username='testuser')
    pen_item = SupplyItem.objects.get(item_code='PEN-001')
    paper_item = SupplyItem.objects.get(item_code='PAP-001')
    
    # Create a supply request
    request = SupplyRequest.objects.create(
        requester=user,
        department=user.userprofile.department,
        justification='Need supplies for upcoming project work and daily operations',
        required_date=date.today() + timedelta(days=7),
        notes='Urgent request for project deadline'
    )
    
    # Add request items
    RequestItem.objects.create(
        request=request,
        supply_item=pen_item,
        quantity_requested=5,
        unit_price=pen_item.unit_cost
    )
    
    RequestItem.objects.create(
        request=request,
        supply_item=paper_item,
        quantity_requested=3,
        unit_price=paper_item.unit_cost
    )
    
    print(f"✓ Created supply request: {request.request_id}")
    print(f"  - Status: {request.status}")
    print(f"  - Total items: {request.total_items}")
    print(f"  - Total cost: ${request.total_cost}")
    
    return request

def test_request_validation():
    """Test request validation"""
    print("\nTesting request validation...")
    
    user = User.objects.get(username='testuser')
    usb_item = SupplyItem.objects.get(item_code='USB-001')
    
    # Test requesting more than available stock
    try:
        request = SupplyRequest.objects.create(
            requester=user,
            department=user.userprofile.department,
            justification='Testing stock validation',
            required_date=date.today() + timedelta(days=5)
        )
        
        # This should work (within stock)
        RequestItem.objects.create(
            request=request,
            supply_item=usb_item,
            quantity_requested=10,  # Available: 15
            unit_price=usb_item.unit_cost
        )
        print(f"✓ Request within stock limits created successfully")
        
        # Clean up
        request.delete()
        
    except Exception as e:
        print(f"✗ Error in validation test: {e}")

def display_summary():
    """Display summary of current data"""
    print("\n" + "="*50)
    print("SUPPLY REQUEST SYSTEM SUMMARY")
    print("="*50)
    
    print(f"Users: {User.objects.count()}")
    print(f"Categories: {Category.objects.count()}")
    print(f"Supply Items: {SupplyItem.objects.count()}")
    print(f"Supply Requests: {SupplyRequest.objects.count()}")
    print(f"Request Items: {RequestItem.objects.count()}")
    
    print("\nRecent Requests:")
    for request in SupplyRequest.objects.order_by('-requested_date')[:3]:
        print(f"  - {request.request_id}: {request.status} ({request.total_items} items)")
    
    print("\nLow Stock Items:")
    for item in SupplyItem.objects.filter(current_stock__lte=10):
        status = "LOW STOCK" if item.is_low_stock else "OK"
        print(f"  - {item.name}: {item.current_stock} {item.unit_of_measure} ({status})")

if __name__ == '__main__':
    try:
        create_test_data()
        request = test_supply_request_creation()
        test_request_validation()
        display_summary()
        
        print("\n" + "="*50)
        print("✅ SUPPLY REQUEST SYSTEM TEST COMPLETED SUCCESSFULLY!")
        print("="*50)
        print("\nYou can now:")
        print("1. Run the server: uv run python manage.py runserver")
        print("2. Login with:")
        print("   - Username: testuser, Password: testpass123 (Department User)")
        print("   - Username: gsostaff, Password: testpass123 (GSO Staff)")
        print("3. Test the supply request functionality")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()