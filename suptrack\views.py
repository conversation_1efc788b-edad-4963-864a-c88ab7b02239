from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, F, Case, When
from django.db import transaction
from django.utils import timezone
from django.template.loader import render_to_string
import json

from .models import SupplyRequest, RequestItem, SupplyItem, Category, UserProfile, StockTransaction, QRCode
from .forms import (
    SupplyRequestForm, RequestItemFormSet, SupplyRequestFilterForm, 
    QuickRequestForm, RequestItemForm, RequestApprovalForm, RequestRejectionForm, ItemReleaseForm
)
from .decorators import role_required
from .services import QRCodeService, InventoryService, NotificationService
import json
from django.views.decorators.csrf import csrf_exempt

def home(request):
    """Landing page view"""
    return render(request, 'home.html')

def test_foundation(request):
    """Test view to verify foundation setup"""
    return render(request, 'test_foundation.html')

def test_htmx(request):
    """Test HTMX functionality"""
    return HttpResponse('<p class="text-green-600 font-semibold">HTMX is working correctly!</p>')

@require_http_methods(["POST"])
def demo_qr_generate(request):
    """Demo endpoint for QR code generation"""
    try:
        data = json.loads(request.body)
        item_name = data.get('item_name', 'Demo Item')
        
        # Simulate QR code generation
        qr_data = {
            'item_id': 'DEMO-001',
            'item_name': item_name,
            'generated_at': '2025-01-23T22:00:00Z',
            'qr_code_url': '/static/demo-qr.png'  # Placeholder
        }
        
        return JsonResponse({
            'success': True,
            'message': f'QR code generated for {item_name}',
            'data': qr_data
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'Failed to generate QR code',
            'error': str(e)
        }, status=400)

def demo_analytics(request):
    """Demo endpoint for analytics data"""
    analytics_data = {
        'total_items': 1247,
        'low_stock_alerts': 8,
        'recent_activity': 156,
        'efficiency_score': 94.2,
        'monthly_usage': [
            {'month': 'Jan', 'value': 120},
            {'month': 'Feb', 'value': 135},
            {'month': 'Mar', 'value': 98},
            {'month': 'Apr', 'value': 167},
            {'month': 'May', 'value': 142},
            {'month': 'Jun', 'value': 189},
        ]
    }
    
    return JsonResponse(analytics_data)

def demo_search(request):
    """Demo search functionality"""
    query = request.GET.get('q', '').lower()
    
    # Demo inventory items
    demo_items = [
        {'id': 1, 'name': 'Office Supplies - Pens', 'category': 'Stationery', 'stock': 45, 'status': 'In Stock'},
        {'id': 2, 'name': 'Printer Paper A4', 'category': 'Office', 'stock': 12, 'status': 'Low Stock'},
        {'id': 3, 'name': 'Laptop Chargers', 'category': 'Electronics', 'stock': 8, 'status': 'In Stock'},
        {'id': 4, 'name': 'Coffee Supplies', 'category': 'Kitchen', 'stock': 3, 'status': 'Critical'},
        {'id': 5, 'name': 'Cleaning Supplies', 'category': 'Maintenance', 'stock': 23, 'status': 'In Stock'},
    ]
    
    if query:
        filtered_items = [item for item in demo_items if query in item['name'].lower()]
    else:
        filtered_items = demo_items
    
    return JsonResponse({
        'items': filtered_items,
        'total': len(filtered_items)
    })

# Supply Request Management Views

@login_required
@role_required(['department_user', 'admin'])
def create_supply_request(request):
    """Create a new supply request with multiple items"""
    if request.method == 'POST':
        form = SupplyRequestForm(request.POST, user=request.user)
        formset = RequestItemFormSet(request.POST, prefix='items')
        
        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    # Save the supply request
                    supply_request = form.save()
                    
                    # Save request items
                    for item_form in formset:
                        if item_form.cleaned_data and not item_form.cleaned_data.get('DELETE', False):
                            request_item = item_form.save(commit=False)
                            request_item.request = supply_request
                            request_item.save()
                    
                    messages.success(request, f'Supply request {supply_request.request_id} created successfully!')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="text-green-600 font-semibold">Request {supply_request.request_id} created successfully!</div>',
                            headers={'HX-Trigger': 'requestCreated'}
                        )
                    
                    return redirect('request_detail', request_id=supply_request.request_id)
                    
            except Exception as e:
                messages.error(request, f'Error creating request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                        status=400
                    )
        else:
            if request.headers.get('HX-Request'):
                # Return form with errors for HTMX
                context = {'form': form, 'formset': formset}
                return render(request, 'requests/partials/request_form.html', context)
    else:
        form = SupplyRequestForm(user=request.user)
        formset = RequestItemFormSet(prefix='items')
    
    # Get available items grouped by category
    categories = Category.objects.filter(
        is_active=True,
        supplyitem__is_active=True,
        supplyitem__current_stock__gt=0
    ).distinct().prefetch_related('supplyitem_set')
    
    context = {
        'form': form,
        'formset': formset,
        'categories': categories,
        'page_title': 'Create Supply Request'
    }
    
    return render(request, 'requests/create_request.html', context)


@login_required
@role_required(['department_user', 'admin'])
def quick_request(request):
    """Quick single-item request form"""
    if request.method == 'POST':
        form = QuickRequestForm(request.POST)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create supply request
                    supply_request = SupplyRequest.objects.create(
                        requester=request.user,
                        department=request.user.userprofile.department,
                        justification=form.cleaned_data['justification'],
                        required_date=form.cleaned_data['required_date']
                    )
                    
                    # Create request item
                    RequestItem.objects.create(
                        request=supply_request,
                        supply_item=form.cleaned_data['supply_item'],
                        quantity_requested=form.cleaned_data['quantity'],
                        unit_price=form.cleaned_data['supply_item'].unit_cost
                    )
                    
                    messages.success(request, f'Quick request {supply_request.request_id} created successfully!')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="text-green-600 font-semibold">Quick request {supply_request.request_id} created!</div>',
                            headers={'HX-Trigger': 'requestCreated'}
                        )
                    
                    return redirect('request_detail', request_id=supply_request.request_id)
                    
            except Exception as e:
                messages.error(request, f'Error creating quick request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                        status=400
                    )
        else:
            if request.headers.get('HX-Request'):
                return render(request, 'requests/partials/quick_request_form.html', {'form': form})
    else:
        form = QuickRequestForm()
    
    context = {
        'form': form,
        'page_title': 'Quick Request'
    }
    
    return render(request, 'requests/quick_request.html', context)


@login_required
def request_list(request):
    """List supply requests with filtering and pagination"""
    # Base queryset - users can only see their own requests unless they're GSO staff or admin
    if request.user.userprofile.role in ['gso_staff', 'admin']:
        requests = SupplyRequest.objects.all()
    else:
        requests = SupplyRequest.objects.filter(requester=request.user)
    
    requests = requests.select_related('requester', 'approved_by').prefetch_related('request_items__supply_item')
    
    # Apply filters
    filter_form = SupplyRequestFilterForm(request.GET)
    if filter_form.is_valid():
        if filter_form.cleaned_data.get('status'):
            requests = requests.filter(status=filter_form.cleaned_data['status'])
        
        if filter_form.cleaned_data.get('date_from'):
            requests = requests.filter(requested_date__date__gte=filter_form.cleaned_data['date_from'])
        
        if filter_form.cleaned_data.get('date_to'):
            requests = requests.filter(requested_date__date__lte=filter_form.cleaned_data['date_to'])
        
        if filter_form.cleaned_data.get('search'):
            search_term = filter_form.cleaned_data['search']
            requests = requests.filter(
                Q(request_id__icontains=search_term) |
                Q(justification__icontains=search_term) |
                Q(requester__first_name__icontains=search_term) |
                Q(requester__last_name__icontains=search_term)
            )
    
    # Order by most recent first
    requests = requests.order_by('-requested_date')
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'filter_form': filter_form,
        'page_title': 'Supply Requests'
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'requests/partials/request_list.html', context)
    
    return render(request, 'requests/request_list.html', context)


@login_required
def request_detail(request, request_id):
    """View detailed information about a supply request"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Check permissions - users can only view their own requests unless they're GSO staff or admin
    if (request.user != supply_request.requester and 
        request.user.userprofile.role not in ['gso_staff', 'admin']):
        messages.error(request, "You don't have permission to view this request.")
        return redirect('request_list')
    
    # Get request items with supply item details
    request_items = supply_request.request_items.select_related('supply_item__category').all()
    
    # Calculate totals
    total_items = request_items.count()
    total_cost = sum(item.total_cost for item in request_items)
    
    context = {
        'supply_request': supply_request,
        'request_items': request_items,
        'total_items': total_items,
        'total_cost': total_cost,
        'page_title': f'Request {request_id}'
    }
    
    return render(request, 'requests/request_detail.html', context)


@login_required
@require_http_methods(["GET"])
def get_item_details(request):
    """HTMX endpoint to get supply item details"""
    item_id = request.GET.get('supply_item')
    
    if not item_id:
        return HttpResponse('<div class="text-gray-500">Select an item to see details</div>')
    
    try:
        item = SupplyItem.objects.select_related('category').get(id=item_id, is_active=True)
        
        context = {
            'item': item,
            'stock_status': 'Low Stock' if item.is_low_stock else 'In Stock',
            'stock_class': 'text-red-600' if item.is_low_stock else 'text-green-600'
        }
        
        return render(request, 'requests/partials/item_details.html', context)
        
    except SupplyItem.DoesNotExist:
        return HttpResponse('<div class="text-red-500">Item not found</div>')


@login_required
@require_http_methods(["GET", "POST"])
def add_request_item(request):
    """HTMX endpoint to add a new request item form"""
    # Get the next form index from either POST or GET parameters
    total_forms = int(request.GET.get('total_forms', request.POST.get('items-TOTAL_FORMS', 0)))
    
    # Create a new form with the correct prefix
    form = RequestItemForm(prefix=f'items-{total_forms}')
    
    context = {
        'form': form,
        'form_index': total_forms
    }
    
    return render(request, 'requests/partials/request_item_form.html', context)


@login_required
@require_http_methods(["GET"])
def validate_stock(request):
    """HTMX endpoint to validate stock availability"""
    item_id = request.GET.get('item_id')
    quantity = request.GET.get('quantity')
    
    if not item_id or not quantity:
        return HttpResponse('')
    
    try:
        item = SupplyItem.objects.get(id=item_id, is_active=True)
        quantity = int(quantity)
        
        if quantity <= 0:
            return HttpResponse('<div class="text-red-600 text-sm">Quantity must be greater than 0</div>')
        
        if quantity > item.current_stock:
            return HttpResponse(f'<div class="text-red-600 text-sm">Requested quantity ({quantity}) exceeds available stock ({item.current_stock})</div>')
        
        if quantity > item.current_stock * 0.8:  # Warning if requesting more than 80% of stock
            return HttpResponse(f'<div class="text-yellow-600 text-sm">Warning: Requesting {quantity} of {item.current_stock} available (high usage)</div>')
        
        return HttpResponse(f'<div class="text-green-600 text-sm">✓ {quantity} units available</div>')
        
    except (SupplyItem.DoesNotExist, ValueError):
        return HttpResponse('<div class="text-red-600 text-sm">Invalid item or quantity</div>')


@login_required
@require_http_methods(["DELETE"])
def cancel_request(request, request_id):
    """Cancel a pending supply request"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Check permissions
    if (request.user != supply_request.requester and 
        request.user.userprofile.role not in ['admin']):
        return HttpResponse('Unauthorized', status=403)
    
    # Can only cancel pending requests
    if supply_request.status != 'pending':
        return HttpResponse('Can only cancel pending requests', status=400)
    
    try:
        supply_request.status = 'cancelled'
        supply_request.save()
        
        messages.success(request, f'Request {request_id} has been cancelled.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                '<div class="text-green-600 font-semibold">Request cancelled successfully</div>',
                headers={'HX-Trigger': 'requestUpdated'}
            )
        
        return redirect('request_list')
        
    except Exception as e:
        return HttpResponse(f'Error cancelling request: {str(e)}', status=500)


@login_required
def request_dashboard(request):
    """Dashboard showing request statistics and recent activity"""
    user_profile = request.user.userprofile
    
    if user_profile.role in ['gso_staff', 'admin']:
        # GSO staff and admin see all requests
        requests = SupplyRequest.objects.all()
    else:
        # Department users see only their requests
        requests = SupplyRequest.objects.filter(requester=request.user)
    
    # Calculate statistics
    stats = {
        'total_requests': requests.count(),
        'pending_requests': requests.filter(status='pending').count(),
        'approved_requests': requests.filter(status='approved').count(),
        'rejected_requests': requests.filter(status='rejected').count(),
        'completed_requests': requests.filter(status='completed').count(),
    }
    
    # Recent requests
    recent_requests = requests.select_related('requester', 'approved_by').order_by('-requested_date')[:5]
    
    # Pending requests for GSO staff
    pending_requests = None
    if user_profile.role in ['gso_staff', 'admin']:
        pending_requests = SupplyRequest.objects.filter(status='pending').select_related('requester')[:10]
    
    context = {
        'stats': stats,
        'recent_requests': recent_requests,
        'pending_requests': pending_requests,
        'user_role': user_profile.role,
        'page_title': 'Request Dashboard'
    }
    
    return render(request, 'requests/dashboard.html', context)


# Approval Workflow Views

@login_required
@role_required(['gso_staff', 'admin'])
def pending_requests_dashboard(request):
    """Dashboard for GSO staff to view and manage pending requests"""
    # Get all pending requests with related data
    pending_requests = SupplyRequest.objects.filter(
        status='pending'
    ).select_related(
        'requester', 'requester__userprofile'
    ).prefetch_related(
        'request_items__supply_item__category'
    ).order_by('-requested_date')
    
    # Apply filters if provided
    department_filter = request.GET.get('department')
    date_filter = request.GET.get('date_from')
    
    if department_filter:
        pending_requests = pending_requests.filter(department__icontains=department_filter)
    
    if date_filter:
        pending_requests = pending_requests.filter(requested_date__date__gte=date_filter)
    
    # Pagination
    paginator = Paginator(pending_requests, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get unique departments for filter
    departments = SupplyRequest.objects.filter(
        status='pending'
    ).values_list('department', flat=True).distinct()
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'selected_department': department_filter,
        'selected_date': date_filter,
        'page_title': 'Pending Requests Dashboard'
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'requests/partials/pending_requests_list.html', context)
    
    return render(request, 'requests/pending_requests_dashboard.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def review_request(request, request_id):
    """Review interface for supply request approval/rejection"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Only allow review of pending requests
    if supply_request.status != 'pending':
        messages.error(request, "This request is no longer pending review.")
        return redirect('pending_requests_dashboard')
    
    # Get request items with supply item details
    request_items = supply_request.request_items.select_related(
        'supply_item__category'
    ).all()
    
    # Validate stock availability
    stock_validation = InventoryService.validate_stock_availability(request_items)
    
    context = {
        'supply_request': supply_request,
        'request_items': request_items,
        'stock_validation': stock_validation,
        'page_title': f'Review Request {request_id}'
    }
    
    return render(request, 'requests/review_request.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def approve_request(request, request_id):
    """Approve a supply request with quantity adjustments"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Only allow approval of pending requests
    if supply_request.status != 'pending':
        messages.error(request, "This request is no longer pending approval.")
        return redirect('pending_requests_dashboard')
    
    request_items = supply_request.request_items.select_related('supply_item').all()
    
    if request.method == 'POST':
        form = RequestApprovalForm(request.POST, request_items=request_items)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Update request status
                    supply_request.status = 'approved'
                    supply_request.approved_by = request.user
                    supply_request.approved_date = timezone.now()
                    supply_request.notes = form.cleaned_data.get('notes', '')
                    supply_request.save()
                    
                    # Update approved quantities for each item
                    for item in request_items:
                        field_name = f'quantity_approved_{item.id}'
                        approved_qty = form.cleaned_data.get(field_name, 0)
                        item.quantity_approved = approved_qty
                        item.save()
                        
                        # Create stock transaction for approved items
                        if approved_qty > 0:
                            InventoryService.create_stock_transaction(
                                supply_item=item.supply_item,
                                transaction_type='out',
                                quantity=-approved_qty,  # Negative for reservation
                                performed_by=request.user,
                                reference_document=supply_request.request_id,
                                notes=f'Approved for release - {form.cleaned_data.get("notes", "")}',
                                request_item=item
                            )
                    
                    # Send notification
                    NotificationService.notify_request_approved(supply_request, request.user)
                    
                    messages.success(request, f'Request {request_id} has been approved successfully!')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="text-green-600 font-semibold">Request {request_id} approved successfully!</div>',
                            headers={'HX-Trigger': 'requestApproved'}
                        )
                    
                    return redirect('request_detail', request_id=request_id)
                    
            except Exception as e:
                messages.error(request, f'Error approving request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                        status=400
                    )
        else:
            if request.headers.get('HX-Request'):
                context = {'form': form, 'supply_request': supply_request, 'request_items': request_items}
                return render(request, 'requests/partials/approval_form.html', context)
    else:
        form = RequestApprovalForm(request_items=request_items)
    
    context = {
        'form': form,
        'supply_request': supply_request,
        'request_items': request_items,
        'page_title': f'Approve Request {request_id}'
    }
    
    return render(request, 'requests/approve_request.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def reject_request(request, request_id):
    """Reject a supply request with reason"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Only allow rejection of pending requests
    if supply_request.status != 'pending':
        messages.error(request, "This request is no longer pending review.")
        return redirect('pending_requests_dashboard')
    
    if request.method == 'POST':
        form = RequestRejectionForm(request.POST)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Update request status
                    supply_request.status = 'rejected'
                    supply_request.approved_by = request.user  # Track who rejected it
                    supply_request.approved_date = timezone.now()
                    supply_request.rejection_reason = form.cleaned_data['rejection_reason']
                    supply_request.save()
                    
                    # Send notification if requested
                    if form.cleaned_data.get('notify_requester', True):
                        NotificationService.notify_request_rejected(
                            supply_request, 
                            request.user, 
                            form.cleaned_data['rejection_reason']
                        )
                    
                    messages.success(request, f'Request {request_id} has been rejected.')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="text-green-600 font-semibold">Request {request_id} rejected successfully!</div>',
                            headers={'HX-Trigger': 'requestRejected'}
                        )
                    
                    return redirect('pending_requests_dashboard')
                    
            except Exception as e:
                messages.error(request, f'Error rejecting request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                        status=400
                    )
        else:
            if request.headers.get('HX-Request'):
                context = {'form': form, 'supply_request': supply_request}
                return render(request, 'requests/partials/rejection_form.html', context)
    else:
        form = RequestRejectionForm()
    
    context = {
        'form': form,
        'supply_request': supply_request,
        'page_title': f'Reject Request {request_id}'
    }
    
    return render(request, 'requests/reject_request.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def release_items(request, request_id):
    """Release approved items with QR code generation"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Only allow release of approved requests
    if supply_request.status != 'approved':
        messages.error(request, "This request is not approved for release.")
        return redirect('request_detail', request_id=request_id)
    
    # Get approved items
    approved_items = supply_request.request_items.filter(
        quantity_approved__gt=0
    ).select_related('supply_item').all()
    
    if not approved_items:
        messages.error(request, "No approved items found for release.")
        return redirect('request_detail', request_id=request_id)
    
    if request.method == 'POST':
        form = ItemReleaseForm(request.POST, approved_items=approved_items)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    released_items = []
                    generated_qr_codes = []
                    
                    # Process each approved item
                    for item in approved_items:
                        if item.quantity_approved and item.quantity_approved > 0:
                            field_name = f'release_quantity_{item.id}'
                            qr_field_name = f'generate_qr_{item.id}'
                            
                            release_qty = form.cleaned_data.get(field_name, 0)
                            generate_qr = form.cleaned_data.get(qr_field_name, False)
                            
                            if release_qty > 0:
                                # Process item release
                                transaction_record = InventoryService.process_item_release(
                                    request_item=item,
                                    quantity_released=release_qty,
                                    performed_by=request.user,
                                    notes=form.cleaned_data.get('release_notes', '')
                                )
                                
                                released_items.append({
                                    'item': item,
                                    'quantity': release_qty,
                                    'transaction': transaction_record
                                })
                                
                                # Generate QR code if requested
                                if generate_qr:
                                    qr_code = QRCodeService.generate_qr_code(item, release_qty)
                                    generated_qr_codes.append(qr_code)
                    
                    # Update request status to released
                    supply_request.status = 'released'
                    supply_request.save()
                    
                    # Send notification
                    NotificationService.notify_items_released(supply_request, request.user)
                    
                    messages.success(request, f'Items released successfully for request {request_id}!')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="text-green-600 font-semibold">Items released successfully!</div>',
                            headers={'HX-Trigger': 'itemsReleased'}
                        )
                    
                    # Redirect to release confirmation page
                    return redirect('release_confirmation', request_id=request_id)
                    
            except Exception as e:
                messages.error(request, f'Error releasing items: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                        status=400
                    )
        else:
            if request.headers.get('HX-Request'):
                context = {'form': form, 'supply_request': supply_request, 'approved_items': approved_items}
                return render(request, 'requests/partials/release_form.html', context)
    else:
        form = ItemReleaseForm(approved_items=approved_items)
    
    context = {
        'form': form,
        'supply_request': supply_request,
        'approved_items': approved_items,
        'page_title': f'Release Items - Request {request_id}'
    }
    
    return render(request, 'requests/release_items.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def release_confirmation(request, request_id):
    """Show release confirmation with QR codes"""
    supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
    
    # Get released items and their QR codes
    released_items = supply_request.request_items.filter(
        quantity_approved__gt=0
    ).select_related('supply_item').prefetch_related('qr_codes').all()
    
    # Get recent transactions for this request
    recent_transactions = StockTransaction.objects.filter(
        request_item__request=supply_request,
        transaction_type='out'
    ).select_related('supply_item').order_by('-timestamp')[:10]
    
    context = {
        'supply_request': supply_request,
        'released_items': released_items,
        'recent_transactions': recent_transactions,
        'page_title': f'Release Confirmation - Request {request_id}'
    }
    
    return render(request, 'requests/release_confirmation.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def approved_requests_list(request):
    """List of approved requests ready for release"""
    approved_requests = SupplyRequest.objects.filter(
        status='approved'
    ).select_related(
        'requester', 'approved_by'
    ).prefetch_related(
        'request_items__supply_item'
    ).order_by('-approved_date')
    
    # Apply filters
    department_filter = request.GET.get('department')
    if department_filter:
        approved_requests = approved_requests.filter(department__icontains=department_filter)
    
    # Pagination
    paginator = Paginator(approved_requests, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'selected_department': department_filter,
        'page_title': 'Approved Requests'
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'requests/partials/approved_requests_list.html', context)
    
    return render(request, 'requests/approved_requests_list.html', context)
# Inventory Management Views

@login_required
@role_required(['gso_staff', 'admin'])
def inventory_dashboard(request):
    """Main inventory dashboard with real-time stock levels"""
    # Get all active supply items with related data
    supply_items = SupplyItem.objects.filter(
        is_active=True
    ).select_related('category').prefetch_related('transactions').annotate(
        total_transactions=Count('transactions'),
        recent_transactions=Count('transactions', filter=Q(transactions__timestamp__gte=timezone.now() - timezone.timedelta(days=30)))
    )
    
    # Apply filters
    category_filter = request.GET.get('category')
    stock_filter = request.GET.get('stock_status')
    search_query = request.GET.get('search')
    
    if category_filter:
        supply_items = supply_items.filter(category_id=category_filter)
    
    if stock_filter == 'low':
        supply_items = supply_items.filter(current_stock__lte=F('minimum_threshold'))
    elif stock_filter == 'out':
        supply_items = supply_items.filter(current_stock=0)
    elif stock_filter == 'normal':
        supply_items = supply_items.filter(current_stock__gt=F('minimum_threshold'))
    
    if search_query:
        supply_items = supply_items.filter(
            Q(name__icontains=search_query) |
            Q(item_code__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Order by stock status (low stock first) then by name
    supply_items = supply_items.order_by(
        Case(
            When(current_stock__lte=F('minimum_threshold'), then=0),
            When(current_stock=0, then=1),
            default=2
        ),
        'name'
    )
    
    # Pagination
    paginator = Paginator(supply_items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Calculate dashboard statistics
    total_items = SupplyItem.objects.filter(is_active=True).count()
    low_stock_count = SupplyItem.objects.filter(
        is_active=True,
        current_stock__lte=F('minimum_threshold')
    ).count()
    out_of_stock_count = SupplyItem.objects.filter(
        is_active=True,
        current_stock=0
    ).count()
    total_value = SupplyItem.objects.filter(
        is_active=True
    ).aggregate(
        total=Sum(F('current_stock') * F('unit_cost'))
    )['total'] or 0
    
    # Get categories for filter dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')
    
    # Recent transactions for activity feed
    recent_transactions = StockTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).order_by('-timestamp')[:10]
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'recent_transactions': recent_transactions,
        'stats': {
            'total_items': total_items,
            'low_stock_count': low_stock_count,
            'out_of_stock_count': out_of_stock_count,
            'total_value': total_value,
        },
        'filters': {
            'category': category_filter,
            'stock_status': stock_filter,
            'search': search_query,
        },
        'page_title': 'Inventory Dashboard'
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'inventory/partials/inventory_list.html', context)
    
    return render(request, 'inventory/dashboard.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def inventory_item_detail(request, item_id):
    """Detailed view of a supply item with transaction history"""
    supply_item = get_object_or_404(
        SupplyItem.objects.select_related('category'),
        id=item_id,
        is_active=True
    )
    
    # Get transaction history with pagination
    transactions = supply_item.transactions.select_related(
        'performed_by', 'request_item__request'
    ).order_by('-timestamp')
    
    # Apply date filter if provided
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    transaction_type = request.GET.get('transaction_type')
    
    if date_from:
        transactions = transactions.filter(timestamp__date__gte=date_from)
    if date_to:
        transactions = transactions.filter(timestamp__date__lte=date_to)
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)
    
    # Pagination for transactions
    paginator = Paginator(transactions, 15)
    page_number = request.GET.get('page')
    transactions_page = paginator.get_page(page_number)
    
    # Calculate statistics
    total_in = transactions.filter(
        transaction_type__in=['in', 'return', 'adjustment'],
        quantity__gt=0
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    total_out = transactions.filter(
        transaction_type__in=['out', 'damaged'],
        quantity__lt=0
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    adjustments = transactions.filter(
        transaction_type='adjustment'
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    # Recent requests involving this item
    recent_requests = RequestItem.objects.filter(
        supply_item=supply_item
    ).select_related('request__requester').order_by('-request__requested_date')[:5]
    
    context = {
        'supply_item': supply_item,
        'transactions_page': transactions_page,
        'recent_requests': recent_requests,
        'transaction_stats': {
            'total_in': abs(total_in),
            'total_out': abs(total_out),
            'adjustments': adjustments,
        },
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'transaction_type': transaction_type,
        },
        'transaction_types': StockTransaction.TRANSACTION_TYPES,
        'page_title': f'Item Details - {supply_item.name}'
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'inventory/partials/transaction_history.html', context)
    
    return render(request, 'inventory/item_detail.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def add_inventory_stock(request):
    """Add new stock to inventory items"""
    if request.method == 'POST':
        supply_item_id = request.POST.get('supply_item')
        quantity = request.POST.get('quantity')
        reference_document = request.POST.get('reference_document', '')
        notes = request.POST.get('notes', '')
        
        try:
            supply_item = SupplyItem.objects.get(id=supply_item_id, is_active=True)
            quantity = int(quantity)
            
            if quantity <= 0:
                raise ValueError("Quantity must be greater than 0")
            
            with transaction.atomic():
                # Create stock transaction
                stock_transaction = StockTransaction.objects.create(
                    supply_item=supply_item,
                    transaction_type='in',
                    quantity=quantity,
                    reference_document=reference_document,
                    performed_by=request.user,
                    notes=notes
                )
                
                messages.success(
                    request, 
                    f'Added {quantity} units of {supply_item.name} to inventory'
                )
                
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-green-600 font-semibold">Added {quantity} units successfully!</div>',
                        headers={'HX-Trigger': 'stockUpdated'}
                    )
                
                return redirect('inventory_item_detail', item_id=supply_item.id)
                
        except (SupplyItem.DoesNotExist, ValueError) as e:
            error_msg = str(e) if isinstance(e, ValueError) else "Invalid item selected"
            messages.error(request, f'Error adding stock: {error_msg}')
            
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-red-600 font-semibold">Error: {error_msg}</div>',
                    status=400
                )
        except Exception as e:
            messages.error(request, f'Unexpected error: {str(e)}')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                    status=500
                )
    
    # Get all active supply items for the form
    supply_items = SupplyItem.objects.filter(is_active=True).order_by('name')
    
    context = {
        'supply_items': supply_items,
        'page_title': 'Add Inventory Stock'
    }
    
    return render(request, 'inventory/add_stock.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def adjust_inventory_stock(request):
    """Adjust inventory stock levels (corrections)"""
    if request.method == 'POST':
        supply_item_id = request.POST.get('supply_item')
        adjustment_type = request.POST.get('adjustment_type')  # 'increase' or 'decrease'
        quantity = request.POST.get('quantity')
        reason = request.POST.get('reason', '')
        notes = request.POST.get('notes', '')
        
        try:
            supply_item = SupplyItem.objects.get(id=supply_item_id, is_active=True)
            quantity = int(quantity)
            
            if quantity <= 0:
                raise ValueError("Quantity must be greater than 0")
            
            # Determine adjustment quantity (positive for increase, negative for decrease)
            adjustment_quantity = quantity if adjustment_type == 'increase' else -quantity
            
            # Check if decrease would result in negative stock
            if adjustment_type == 'decrease' and supply_item.current_stock < quantity:
                raise ValueError(f"Cannot decrease by {quantity}. Current stock is only {supply_item.current_stock}")
            
            with transaction.atomic():
                # Create stock transaction
                stock_transaction = StockTransaction.objects.create(
                    supply_item=supply_item,
                    transaction_type='adjustment',
                    quantity=adjustment_quantity,
                    reference_document=f"Stock Adjustment - {reason}",
                    performed_by=request.user,
                    notes=notes
                )
                
                action = "increased" if adjustment_type == 'increase' else "decreased"
                messages.success(
                    request, 
                    f'Stock {action} by {quantity} units for {supply_item.name}'
                )
                
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="text-green-600 font-semibold">Stock {action} by {quantity} units!</div>',
                        headers={'HX-Trigger': 'stockUpdated'}
                    )
                
                return redirect('inventory_item_detail', item_id=supply_item.id)
                
        except (SupplyItem.DoesNotExist, ValueError) as e:
            error_msg = str(e) if isinstance(e, ValueError) else "Invalid item selected"
            messages.error(request, f'Error adjusting stock: {error_msg}')
            
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-red-600 font-semibold">Error: {error_msg}</div>',
                    status=400
                )
        except Exception as e:
            messages.error(request, f'Unexpected error: {str(e)}')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                    status=500
                )
    
    # Get all active supply items for the form
    supply_items = SupplyItem.objects.filter(is_active=True).order_by('name')
    
    context = {
        'supply_items': supply_items,
        'page_title': 'Adjust Inventory Stock'
    }
    
    return render(request, 'inventory/adjust_stock.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def low_stock_alerts(request):
    """View items with low stock levels"""
    # Get items at or below minimum threshold
    low_stock_items = SupplyItem.objects.filter(
        is_active=True,
        current_stock__lte=F('minimum_threshold')
    ).select_related('category').order_by('current_stock', 'name')
    
    # Separate critical (out of stock) from low stock
    critical_items = low_stock_items.filter(current_stock=0)
    low_items = low_stock_items.filter(current_stock__gt=0)
    
    context = {
        'critical_items': critical_items,
        'low_items': low_items,
        'total_alerts': low_stock_items.count(),
        'page_title': 'Low Stock Alerts'
    }
    
    return render(request, 'inventory/low_stock_alerts.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def inventory_search(request):
    """HTMX endpoint for real-time inventory search"""
    query = request.GET.get('q', '').strip()
    
    if not query:
        return HttpResponse('<div class="text-gray-500 p-4">Enter search terms to find items</div>')
    
    # Search in item name, code, and description
    items = SupplyItem.objects.filter(
        Q(name__icontains=query) |
        Q(item_code__icontains=query) |
        Q(description__icontains=query),
        is_active=True
    ).select_related('category')[:10]  # Limit to 10 results for performance
    
    context = {
        'items': items,
        'query': query
    }
    
    return render(request, 'inventory/partials/search_results.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def get_inventory_status(request):
    """HTMX endpoint for real-time inventory status updates"""
    # Get current inventory statistics
    stats = {
        'total_items': SupplyItem.objects.filter(is_active=True).count(),
        'low_stock_count': SupplyItem.objects.filter(
            is_active=True,
            current_stock__lte=F('minimum_threshold')
        ).count(),
        'out_of_stock_count': SupplyItem.objects.filter(
            is_active=True,
            current_stock=0
        ).count(),
        'total_value': SupplyItem.objects.filter(
            is_active=True
        ).aggregate(
            total=Sum(F('current_stock') * F('unit_cost'))
        )['total'] or 0,
    }
    
    # Recent activity (last 5 transactions)
    recent_transactions = StockTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).order_by('-timestamp')[:5]
    
    context = {
        'stats': stats,
        'recent_transactions': recent_transactions
    }
    
    return render(request, 'inventory/partials/inventory_status.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def get_item_stock_info(request):
    """HTMX endpoint to get current stock information for an item"""
    item_id = request.GET.get('item_id')
    
    if not item_id:
        return HttpResponse('<div class="text-gray-500">Select an item</div>')
    
    try:
        item = SupplyItem.objects.select_related('category').get(
            id=item_id, 
            is_active=True
        )
        
        context = {
            'item': item,
            'stock_status': 'Critical' if item.current_stock == 0 else 'Low' if item.is_low_stock else 'Normal',
            'stock_class': 'text-red-600' if item.current_stock == 0 else 'text-yellow-600' if item.is_low_stock else 'text-green-600'
        }
        
        return render(request, 'inventory/partials/item_stock_info.html', context)
        
    except SupplyItem.DoesNotExist:
        return HttpResponse('<div class="text-red-500">Item not found</div>')
# QR Code Management Views

@login_required
@role_required(['gso_staff', 'admin'])
def qr_code_management(request):
    """QR code management dashboard"""
    # Get QR code statistics
    total_qr_codes = QRCode.objects.filter(is_active=True).count()
    scanned_qr_codes = QRCode.objects.filter(is_active=True, scanned_count__gt=0).count()
    recent_scans = QRCode.objects.filter(
        is_active=True, 
        last_scanned__isnull=False
    ).order_by('-last_scanned')[:10]
    
    # Get QR codes with filters
    qr_codes = QRCode.objects.filter(is_active=True).select_related(
        'supply_item', 'request_item__request'
    ).order_by('-generated_date')
    
    # Apply filters
    search_query = request.GET.get('search')
    if search_query:
        qr_codes = qr_codes.filter(
            Q(code_id__icontains=search_query) |
            Q(supply_item__name__icontains=search_query) |
            Q(supply_item__item_code__icontains=search_query) |
            Q(request_item__request__request_id__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(qr_codes, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'total_qr_codes': total_qr_codes,
        'scanned_qr_codes': scanned_qr_codes,
        'recent_scans': recent_scans,
        'search_query': search_query,
        'page_title': 'QR Code Management'
    }
    
    return render(request, 'qr_codes/management.html', context)


@login_required
def qr_code_detail(request, code_id):
    """View detailed information about a QR code"""
    qr_code = get_object_or_404(
        QRCode.objects.select_related(
            'supply_item__category', 
            'request_item__request__requester'
        ), 
        code_id=code_id
    )
    
    # Check permissions - users can view QR codes for their requests or if they're GSO staff/admin
    user_profile = request.user.userprofile
    if (user_profile.role not in ['gso_staff', 'admin'] and 
        request.user != qr_code.request_item.request.requester):
        messages.error(request, "You don't have permission to view this QR code.")
        return redirect('home')
    
    # Get QR code data for display
    qr_data = QRCodeService.get_qr_code_data(qr_code)
    
    context = {
        'qr_code': qr_code,
        'qr_data': qr_data,
        'page_title': f'QR Code {code_id}'
    }
    
    return render(request, 'qr_codes/detail.html', context)


@login_required
def qr_code_print_label(request, code_id):
    """Generate printable QR code label"""
    qr_code = get_object_or_404(
        QRCode.objects.select_related(
            'supply_item__category', 
            'request_item__request'
        ), 
        code_id=code_id
    )
    
    # Check permissions
    user_profile = request.user.userprofile
    if (user_profile.role not in ['gso_staff', 'admin'] and 
        request.user != qr_code.request_item.request.requester):
        messages.error(request, "You don't have permission to print this QR code.")
        return redirect('home')
    
    # Get QR code data
    qr_data = QRCodeService.get_qr_code_data(qr_code)
    
    context = {
        'qr_code': qr_code,
        'qr_data': qr_data,
        'print_date': timezone.now(),
        'page_title': f'Print QR Label {code_id}'
    }
    
    return render(request, 'qr_codes/print_label.html', context)


@login_required
@role_required(['gso_staff', 'admin'])
def regenerate_qr_code(request, code_id):
    """Regenerate QR code for damaged codes"""
    qr_code = get_object_or_404(QRCode, code_id=code_id)
    
    if request.method == 'POST':
        try:
            # Generate new QR code
            new_qr_code = QRCodeService.regenerate_qr_code(qr_code)
            
            messages.success(
                request, 
                f'QR code regenerated successfully. New code ID: {new_qr_code.code_id}'
            )
            
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-green-600 font-semibold">QR code regenerated: {new_qr_code.code_id}</div>',
                    headers={'HX-Trigger': 'qrRegenerated'}
                )
            
            return redirect('qr_code_detail', code_id=new_qr_code.code_id)
            
        except Exception as e:
            messages.error(request, f'Error regenerating QR code: {str(e)}')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    f'<div class="text-red-600 font-semibold">Error: {str(e)}</div>',
                    status=400
                )
    
    context = {
        'qr_code': qr_code,
        'page_title': f'Regenerate QR Code {code_id}'
    }
    
    return render(request, 'qr_codes/regenerate.html', context)


@login_required
@require_http_methods(["POST"])
def scan_qr_code(request):
    """Process QR code scan from mobile device"""
    try:
        data = json.loads(request.body)
        code_id = data.get('code_id')
        scan_type = data.get('scan_type', 'general')
        
        if not code_id:
            return JsonResponse({
                'success': False,
                'error': 'QR code ID is required'
            }, status=400)
        
        # Process the scan
        scan_result = QRCodeService.process_qr_scan(
            code_id=code_id,
            scanned_by=request.user,
            scan_type=scan_type
        )
        
        return JsonResponse(scan_result)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Scan processing error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def validate_qr_data(request):
    """Validate QR code data from scanner"""
    try:
        data = json.loads(request.body)
        qr_data = data.get('qr_data')
        
        if not qr_data:
            return JsonResponse({
                'success': False,
                'error': 'QR data is required'
            }, status=400)
        
        # Validate the QR code data
        validation_result = QRCodeService.validate_qr_code(qr_data)
        
        if validation_result['success']:
            # Also verify the QR code exists in the system
            qr_data_dict = validation_result['data']
            verification_result = QRCodeService.verify_qr_code(qr_data_dict.get('code_id'))
            
            if verification_result['success']:
                return JsonResponse({
                    'success': True,
                    'message': 'QR code is valid',
                    'qr_data': verification_result['data']
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': verification_result['error']
                })
        else:
            return JsonResponse(validation_result)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=500)


@login_required
def qr_scanner_interface(request):
    """Mobile QR code scanner interface"""
    context = {
        'page_title': 'QR Code Scanner',
        'scan_types': [
            ('general', 'General Scan'),
            ('issuance', 'Item Issuance'),
            ('return', 'Item Return'),
            ('audit', 'Inventory Audit')
        ]
    }
    
    return render(request, 'qr_codes/scanner.html', context)


@login_required
@require_http_methods(["GET"])
def get_qr_codes_for_request(request, request_id):
    """HTMX endpoint to get QR codes for a specific request"""
    try:
        supply_request = get_object_or_404(SupplyRequest, request_id=request_id)
        
        # Check permissions
        user_profile = request.user.userprofile
        if (user_profile.role not in ['gso_staff', 'admin'] and 
            request.user != supply_request.requester):
            return HttpResponse('Unauthorized', status=403)
        
        qr_codes = QRCodeService.get_qr_codes_for_request(request_id)
        
        context = {
            'qr_codes': qr_codes,
            'supply_request': supply_request
        }
        
        return render(request, 'qr_codes/partials/request_qr_codes.html', context)
        
    except Exception as e:
        return HttpResponse(f'Error loading QR codes: {str(e)}', status=500)


@login_required
@require_http_methods(["GET"])
def get_qr_codes_for_item(request, item_id):
    """HTMX endpoint to get QR codes for a specific supply item"""
    try:
        supply_item = get_object_or_404(SupplyItem, id=item_id)
        qr_codes = QRCodeService.get_qr_codes_for_item(item_id)
        
        context = {
            'qr_codes': qr_codes,
            'supply_item': supply_item
        }
        
        return render(request, 'qr_codes/partials/item_qr_codes.html', context)
        
    except Exception as e:
        return HttpResponse(f'Error loading QR codes: {str(e)}', status=500)

# Mob
ile QR Code Scanning System Views

@login_required
def qr_scanner_interface(request):
    """Mobile QR code scanner interface"""
    context = {
        'page_title': 'QR Code Scanner',
        'user_role': request.user.userprofile.role
    }
    return render(request, 'qr_codes/scanner_interface.html', context)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def scan_qr_code(request):
    """API endpoint to process QR code scans"""
    try:
        data = json.loads(request.body)
        qr_data = data.get('qr_data')
        scan_type = data.get('scan_type', 'general')
        
        if not qr_data:
            return JsonResponse({
                'success': False,
                'error': 'No QR code data provided'
            }, status=400)
        
        # Validate QR code data format
        validation_result = QRCodeService.validate_qr_code(qr_data)
        if not validation_result['success']:
            return JsonResponse({
                'success': False,
                'error': validation_result['error']
            }, status=400)
        
        validated_data = validation_result['data']
        
        # Find QR code by extracting code_id from data or using item/request info
        try:
            # Try to find QR code by item and request combination
            qr_code = QRCode.objects.select_related(
                'supply_item', 'request_item__request'
            ).get(
                supply_item_id=validated_data['item_id'],
                request_item__request__request_id=validated_data['request_id'],
                is_active=True
            )
            
            # Process the scan
            scan_result = QRCodeService.process_qr_scan(
                qr_code.code_id, 
                request.user, 
                scan_type
            )
            
            if scan_result['success']:
                # Create scan activity log
                ScanActivity.objects.create(
                    qr_code=qr_code,
                    scanned_by=request.user,
                    scan_type=scan_type,
                    scan_data=validated_data,
                    success=True
                )
                
                return JsonResponse({
                    'success': True,
                    'message': scan_result['message'],
                    'qr_data': scan_result['qr_data'],
                    'scan_type': scan_type,
                    'actions_available': get_available_actions(qr_code, request.user)
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': scan_result['error']
                }, status=400)
                
        except QRCode.DoesNotExist:
            # Log failed scan attempt
            ScanActivity.objects.create(
                qr_code=None,
                scanned_by=request.user,
                scan_type=scan_type,
                scan_data=validated_data,
                success=False,
                error_message='QR code not found'
            )
            
            return JsonResponse({
                'success': False,
                'error': 'QR code not found in system'
            }, status=404)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Scan processing error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def process_item_issuance(request):
    """Process item issuance via QR code scan"""
    try:
        data = json.loads(request.body)
        code_id = data.get('code_id')
        recipient_info = data.get('recipient_info', {})
        
        if not code_id:
            return JsonResponse({
                'success': False,
                'error': 'QR code ID required'
            }, status=400)
        
        qr_code = get_object_or_404(QRCode, code_id=code_id, is_active=True)
        
        # Check if user has permission to issue items
        if request.user.userprofile.role not in ['gso_staff', 'admin']:
            return JsonResponse({
                'success': False,
                'error': 'Insufficient permissions for item issuance'
            }, status=403)
        
        # Check if request is approved and ready for release
        if qr_code.request_item.request.status != 'approved':
            return JsonResponse({
                'success': False,
                'error': 'Request is not approved for release'
            }, status=400)
        
        with transaction.atomic():
            # Update request status to released
            supply_request = qr_code.request_item.request
            supply_request.status = 'released'
            supply_request.save()
            
            # Create stock transaction for issuance
            InventoryService.create_stock_transaction(
                supply_item=qr_code.supply_item,
                transaction_type='out',
                quantity=-(qr_code.request_item.quantity_approved or 1),
                performed_by=request.user,
                reference_document=f"QR-{code_id}",
                notes=f"Item issued via QR scan to {recipient_info.get('name', 'Unknown')}"
            )
            
            # Record scan activity
            ScanActivity.objects.create(
                qr_code=qr_code,
                scanned_by=request.user,
                scan_type='issuance',
                scan_data={'recipient': recipient_info},
                success=True
            )
            
            # Update QR code scan count
            qr_code.record_scan(request.user)
            
            return JsonResponse({
                'success': True,
                'message': f'Item {qr_code.supply_item.name} issued successfully',
                'transaction_details': {
                    'item_name': qr_code.supply_item.name,
                    'quantity': qr_code.request_item.quantity_approved,
                    'recipient': recipient_info.get('name', 'Unknown'),
                    'issued_by': request.user.get_full_name() or request.user.username,
                    'issued_at': timezone.now().isoformat()
                }
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Issuance processing error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def process_item_return(request):
    """Process item return via QR code scan"""
    try:
        data = json.loads(request.body)
        code_id = data.get('code_id')
        return_quantity = int(data.get('return_quantity', 1))
        return_reason = data.get('return_reason', '')
        condition = data.get('condition', 'good')
        
        if not code_id:
            return JsonResponse({
                'success': False,
                'error': 'QR code ID required'
            }, status=400)
        
        qr_code = get_object_or_404(QRCode, code_id=code_id, is_active=True)
        
        # Validate return quantity
        if return_quantity <= 0:
            return JsonResponse({
                'success': False,
                'error': 'Return quantity must be greater than 0'
            }, status=400)
        
        with transaction.atomic():
            # Determine transaction type based on condition
            if condition == 'damaged':
                transaction_type = 'damaged'
                stock_adjustment = 0  # Don't add damaged items back to stock
            else:
                transaction_type = 'return'
                stock_adjustment = return_quantity
            
            # Create stock transaction for return
            InventoryService.create_stock_transaction(
                supply_item=qr_code.supply_item,
                transaction_type=transaction_type,
                quantity=stock_adjustment,
                performed_by=request.user,
                reference_document=f"QR-{code_id}",
                notes=f"Item return via QR scan - Condition: {condition}, Reason: {return_reason}"
            )
            
            # Record scan activity
            ScanActivity.objects.create(
                qr_code=qr_code,
                scanned_by=request.user,
                scan_type='return',
                scan_data={
                    'return_quantity': return_quantity,
                    'condition': condition,
                    'reason': return_reason
                },
                success=True
            )
            
            # Update QR code scan count
            qr_code.record_scan(request.user)
            
            return JsonResponse({
                'success': True,
                'message': f'Item {qr_code.supply_item.name} return processed successfully',
                'transaction_details': {
                    'item_name': qr_code.supply_item.name,
                    'return_quantity': return_quantity,
                    'condition': condition,
                    'processed_by': request.user.get_full_name() or request.user.username,
                    'processed_at': timezone.now().isoformat(),
                    'stock_adjusted': stock_adjustment > 0
                }
            })
            
    except (json.JSONDecodeError, ValueError):
        return JsonResponse({
            'success': False,
            'error': 'Invalid data format'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Return processing error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"])
def process_inventory_audit(request):
    """Process inventory audit via QR code scan"""
    try:
        data = json.loads(request.body)
        code_id = data.get('code_id')
        physical_count = int(data.get('physical_count', 0))
        audit_notes = data.get('audit_notes', '')
        
        if not code_id:
            return JsonResponse({
                'success': False,
                'error': 'QR code ID required'
            }, status=400)
        
        qr_code = get_object_or_404(QRCode, code_id=code_id, is_active=True)
        
        # Check if user has permission for inventory audit
        if request.user.userprofile.role not in ['gso_staff', 'admin']:
            return JsonResponse({
                'success': False,
                'error': 'Insufficient permissions for inventory audit'
            }, status=403)
        
        with transaction.atomic():
            # Calculate discrepancy
            system_count = qr_code.supply_item.current_stock
            discrepancy = physical_count - system_count
            
            # Create audit record
            audit_result = {
                'item_name': qr_code.supply_item.name,
                'item_code': qr_code.supply_item.item_code,
                'system_count': system_count,
                'physical_count': physical_count,
                'discrepancy': discrepancy,
                'audited_by': request.user.get_full_name() or request.user.username,
                'audit_date': timezone.now().isoformat(),
                'notes': audit_notes
            }
            
            # If there's a discrepancy, create adjustment transaction
            if discrepancy != 0:
                InventoryService.create_stock_transaction(
                    supply_item=qr_code.supply_item,
                    transaction_type='adjustment',
                    quantity=discrepancy,
                    performed_by=request.user,
                    reference_document=f"AUDIT-QR-{code_id}",
                    notes=f"Inventory audit adjustment - Physical: {physical_count}, System: {system_count}, Notes: {audit_notes}"
                )
            
            # Record scan activity
            ScanActivity.objects.create(
                qr_code=qr_code,
                scanned_by=request.user,
                scan_type='audit',
                scan_data=audit_result,
                success=True
            )
            
            # Update QR code scan count
            qr_code.record_scan(request.user)
            
            return JsonResponse({
                'success': True,
                'message': f'Inventory audit completed for {qr_code.supply_item.name}',
                'audit_result': audit_result,
                'adjustment_made': discrepancy != 0
            })
            
    except (json.JSONDecodeError, ValueError):
        return JsonResponse({
            'success': False,
            'error': 'Invalid data format'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Audit processing error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def validate_qr_data(request):
    """Validate QR code data without processing"""
    qr_data = request.GET.get('qr_data')
    
    if not qr_data:
        return JsonResponse({
            'success': False,
            'error': 'No QR code data provided'
        }, status=400)
    
    try:
        # Validate QR code format
        validation_result = QRCodeService.validate_qr_code(qr_data)
        
        if validation_result['success']:
            validated_data = validation_result['data']
            
            # Try to find the QR code in system
            try:
                qr_code = QRCode.objects.select_related(
                    'supply_item', 'request_item__request'
                ).get(
                    supply_item_id=validated_data['item_id'],
                    request_item__request__request_id=validated_data['request_id'],
                    is_active=True
                )
                
                return JsonResponse({
                    'success': True,
                    'valid': True,
                    'qr_data': QRCodeService.get_qr_code_data(qr_code),
                    'actions_available': get_available_actions(qr_code, request.user)
                })
                
            except QRCode.DoesNotExist:
                return JsonResponse({
                    'success': True,
                    'valid': False,
                    'error': 'QR code not found in system'
                })
        else:
            return JsonResponse({
                'success': True,
                'valid': False,
                'error': validation_result['error']
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_scan_history(request):
    """Get QR code scan history"""
    code_id = request.GET.get('code_id')
    limit = int(request.GET.get('limit', 10))
    
    if code_id:
        # Get history for specific QR code
        scan_activities = ScanActivity.objects.filter(
            qr_code__code_id=code_id
        ).select_related('scanned_by', 'qr_code__supply_item').order_by('-scanned_at')[:limit]
    else:
        # Get recent scan history for user
        scan_activities = ScanActivity.objects.filter(
            scanned_by=request.user
        ).select_related('qr_code__supply_item').order_by('-scanned_at')[:limit]
    
    history_data = []
    for activity in scan_activities:
        history_data.append({
            'id': activity.id,
            'qr_code_id': activity.qr_code.code_id if activity.qr_code else None,
            'item_name': activity.qr_code.supply_item.name if activity.qr_code else 'Unknown',
            'scan_type': activity.scan_type,
            'scanned_at': activity.scanned_at.isoformat(),
            'scanned_by': activity.scanned_by.get_full_name() or activity.scanned_by.username,
            'success': activity.success,
            'error_message': activity.error_message
        })
    
    return JsonResponse({
        'success': True,
        'scan_history': history_data,
        'total_count': len(history_data)
    })


def get_available_actions(qr_code, user):
    """Determine available actions for a QR code based on user role and item status"""
    actions = []
    user_role = user.userprofile.role
    request_status = qr_code.request_item.request.status
    
    # GSO staff and admin have more actions
    if user_role in ['gso_staff', 'admin']:
        if request_status == 'approved':
            actions.append('issue')
        
        actions.extend(['return', 'audit'])
    
    # Department users can return items they requested
    if user == qr_code.request_item.request.requester:
        if request_status in ['released', 'completed']:
            actions.append('return')
    
    # Everyone can view details
    actions.append('view')
    
    return actions