{% extends 'base.html' %}

{% block title %}Foundation Test - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">Foundation Setup Test</h1>
    
    <!-- Test Tailwind CSS -->
    <div class="bg-primary-50 border border-primary-200 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-primary-700 mb-3">✅ Tailwind CSS</h2>
        <p class="text-primary-600">Tailwind CSS is working correctly with custom primary colors.</p>
    </div>
    
    <!-- Test HTMX -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-green-700 mb-3">✅ HTMX</h2>
        <button 
            hx-get="/test-htmx/" 
            hx-target="#htmx-result" 
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded touch-target">
            Test HTMX Request
        </button>
        <div id="htmx-result" class="mt-3 text-green-600"></div>
    </div>
    
    <!-- Test Alpine.js -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6" x-data="{ count: 0 }">
        <h2 class="text-xl font-semibold text-blue-700 mb-3">✅ Alpine.js</h2>
        <p class="text-blue-600 mb-3">Counter: <span x-text="count" class="font-bold"></span></p>
        <button 
            @click="count++" 
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded touch-target mr-2">
            Increment
        </button>
        <button 
            @click="count = 0" 
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded touch-target">
            Reset
        </button>
    </div>
    
    <!-- Test Mobile Responsiveness -->
    <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-purple-700 mb-3">✅ Mobile Responsive Design</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="bg-white p-4 rounded shadow">
                <p class="text-sm text-purple-600">Responsive grid item 1</p>
            </div>
            <div class="bg-white p-4 rounded shadow">
                <p class="text-sm text-purple-600">Responsive grid item 2</p>
            </div>
            <div class="bg-white p-4 rounded shadow mobile-hidden">
                <p class="text-sm text-purple-600">Hidden on mobile</p>
            </div>
        </div>
    </div>
    
    <!-- Test Security Features -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-yellow-700 mb-3">✅ Security Configuration</h2>
        <ul class="text-yellow-600 space-y-1">
            <li>• CSRF Protection: Enabled</li>
            <li>• Session Security: Configured</li>
            <li>• Security Headers: Set</li>
            <li>• File Upload Limits: Applied</li>
        </ul>
    </div>
    
    <!-- Test Package Availability -->
    <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-indigo-700 mb-3">✅ Python Packages</h2>
        <ul class="text-indigo-600 space-y-1">
            <li>• Django 5.2.4: Ready</li>
            <li>• QRCode Library: Available</li>
            <li>• Pillow (PIL): Available</li>
            <li>• ReportLab: Available</li>
            <li>• WeasyPrint: Available (with fallback to ReportLab)</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Test notification system
    setTimeout(() => {
        showNotification('Foundation setup completed successfully!', 'success');
    }, 1000);
</script>
{% endblock %}