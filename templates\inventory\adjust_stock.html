{% extends 'base.html' %}

{% block title %}{{ page_title }} - Smart Supply Management{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'inventory_dashboard' %}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span class="sr-only">Inventory</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'inventory_dashboard' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Inventory</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">Adjust Stock</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="text-center">
        <h1 class="text-2xl font-bold text-gray-900">Adjust Inventory Stock</h1>
        <p class="mt-2 text-sm text-gray-600">Make corrections to inventory stock levels</p>
    </div>

    <!-- Adjust Stock Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="post" class="px-4 py-5 sm:p-6 space-y-6" 
              hx-post="{% url 'adjust_inventory_stock' %}"
              hx-target="#form-messages"
              hx-swap="innerHTML">
            {% csrf_token %}
            
            <!-- Form Messages -->
            <div id="form-messages"></div>
            
            <!-- Supply Item Selection -->
            <div>
                <label for="supply_item" class="block text-sm font-medium text-gray-700">
                    Supply Item <span class="text-red-500">*</span>
                </label>
                <select id="supply_item" 
                        name="supply_item" 
                        required
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
                        hx-get="{% url 'get_item_stock_info' %}"
                        hx-trigger="change"
                        hx-target="#item-info"
                        hx-include="this">
                    <option value="">Select an item...</option>
                    {% for item in supply_items %}
                        <option value="{{ item.id }}" {% if request.GET.item == item.id|stringformat:"s" %}selected{% endif %}>
                            {{ item.item_code }} - {{ item.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>

            <!-- Item Information -->
            <div id="item-info">
                {% if request.GET.item %}
                    {% include 'inventory/partials/item_stock_info.html' %}
                {% else %}
                    <div class="text-gray-500 text-sm">Select an item to see current stock information</div>
                {% endif %}
            </div>

            <!-- Adjustment Type -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Adjustment Type <span class="text-red-500">*</span>
                </label>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input id="increase" 
                               name="adjustment_type" 
                               type="radio" 
                               value="increase" 
                               class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300">
                        <label for="increase" class="ml-3 block text-sm font-medium text-gray-700">
                            <span class="text-green-600">Increase Stock</span>
                            <span class="block text-xs text-gray-500">Add units to current stock (e.g., found additional items)</span>
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input id="decrease" 
                               name="adjustment_type" 
                               type="radio" 
                               value="decrease" 
                               class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300">
                        <label for="decrease" class="ml-3 block text-sm font-medium text-gray-700">
                            <span class="text-red-600">Decrease Stock</span>
                            <span class="block text-xs text-gray-500">Remove units from current stock (e.g., damaged, lost, or miscounted)</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Quantity -->
            <div>
                <label for="quantity" class="block text-sm font-medium text-gray-700">
                    Adjustment Quantity <span class="text-red-500">*</span>
                </label>
                <div class="mt-1 relative rounded-md shadow-sm">
                    <input type="number" 
                           id="quantity" 
                           name="quantity" 
                           min="1" 
                           required
                           class="block w-full pl-3 pr-12 py-2 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                           placeholder="Enter quantity">
                    <div class="absolute inset-y-0 right-0 flex items-center">
                        <span class="text-gray-500 sm:text-sm pr-3">units</span>
                    </div>
                </div>
                <p class="mt-1 text-sm text-gray-500">Enter the number of units to adjust</p>
            </div>

            <!-- Reason -->
            <div>
                <label for="reason" class="block text-sm font-medium text-gray-700">
                    Reason for Adjustment <span class="text-red-500">*</span>
                </label>
                <select id="reason" 
                        name="reason" 
                        required
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md">
                    <option value="">Select a reason...</option>
                    <option value="Physical Count Correction">Physical Count Correction</option>
                    <option value="Damaged Items">Damaged Items</option>
                    <option value="Lost Items">Lost Items</option>
                    <option value="Found Items">Found Items</option>
                    <option value="System Error Correction">System Error Correction</option>
                    <option value="Expired Items">Expired Items</option>
                    <option value="Other">Other</option>
                </select>
            </div>

            <!-- Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700">
                    Detailed Notes <span class="text-red-500">*</span>
                </label>
                <textarea id="notes" 
                          name="notes" 
                          rows="4"
                          required
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Provide detailed explanation for this stock adjustment..."></textarea>
                <p class="mt-1 text-sm text-gray-500">Required: Explain the reason for this adjustment in detail</p>
            </div>

            <!-- Warning Message -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Important</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>Stock adjustments should only be made for corrections and discrepancies. All adjustments are logged and auditable. Make sure to provide detailed notes explaining the reason for the adjustment.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'inventory_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Adjust Stock
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}