{% extends 'base.html' %}
{% load form_filters %}

{% block title %}Approve Request {{ supply_request.request_id }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">Home</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{% url 'pending_requests_dashboard' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Pending Requests</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Approve {{ supply_request.request_id }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Approve Request {{ supply_request.request_id }}</h1>
                <p class="mt-2 text-gray-600">Review and adjust quantities before approval</p>
            </div>
        </div>
    </div>

    <!-- Approval Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Request Summary -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Request Summary</h2>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Requester:</span>
                        <span class="text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Department:</span>
                        <span class="text-gray-900">{{ supply_request.department }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Required Date:</span>
                        <span class="text-gray-900">{{ supply_request.required_date|date:"M d, Y" }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="font-medium text-gray-700">Justification:</span>
                    <p class="text-gray-900 mt-1">{{ supply_request.justification }}</p>
                </div>
            </div>
        </div>

        <!-- Items for Approval -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Items for Approval</h2>
                <p class="text-sm text-gray-600 mt-1">Adjust quantities as needed. Set to 0 to exclude an item from approval.</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Requested
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Available Stock
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Approve Quantity
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit Price
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in request_items %}
                            {% with field_name='quantity_approved_'|add:item.id|stringformat:"s" %}
                                <tr>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item.supply_item.item_code }}</div>
                                        <div class="text-xs text-gray-500">{{ item.supply_item.category.name }}</div>
                                        {% if item.notes %}
                                            <div class="text-xs text-gray-500 mt-1">{{ item.notes }}</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ item.quantity_requested }} {{ item.supply_item.unit_of_measure }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ item.supply_item.current_stock }} {{ item.supply_item.unit_of_measure }}</div>
                                        {% if item.supply_item.is_low_stock %}
                                            <div class="text-xs text-red-600">Low Stock</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="max-w-24">
                                            {{ form|get_field:field_name }}
                                            {% if form.errors|get_item:field_name %}
                                                <div class="text-red-600 text-xs mt-1">
                                                    {{ form.errors|get_item:field_name|join:", " }}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Max: {{ item.supply_item.current_stock|floatformat:0 }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">${{ item.unit_price }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900" id="total-{{ item.id }}">
                                            $<span class="item-total" data-unit-price="{{ item.unit_price }}">{{ item.total_cost }}</span>
                                        </div>
                                    </td>
                                </tr>
                            {% endwith %}
                        {% endfor %}
                    </tbody>
                    <tfoot class="bg-gray-50">
                        <tr>
                            <td colspan="5" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                                Approved Total:
                            </td>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">
                                $<span id="approved-total">0.00</span>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Approval Notes -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Approval Notes</h2>
            </div>
            <div class="px-6 py-4">
                {{ form.notes }}
                {% if form.notes.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        {{ form.notes.errors|join:", " }}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'review_request' supply_request.request_id %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Approve Request
            </button>
        </div>
    </form>
</div>

<script>
    // Calculate totals dynamically
    function updateTotals() {
        let approvedTotal = 0;
        
        document.querySelectorAll('input[name^="quantity_approved_"]').forEach(function(input) {
            const quantity = parseFloat(input.value) || 0;
            const unitPrice = parseFloat(input.closest('tr').querySelector('.item-total').dataset.unitPrice) || 0;
            const itemTotal = quantity * unitPrice;
            
            // Update item total
            input.closest('tr').querySelector('.item-total').textContent = itemTotal.toFixed(2);
            
            // Add to approved total
            approvedTotal += itemTotal;
        });
        
        // Update approved total
        document.getElementById('approved-total').textContent = approvedTotal.toFixed(2);
    }
    
    // Add event listeners to quantity inputs
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('input[name^="quantity_approved_"]').forEach(function(input) {
            input.addEventListener('input', updateTotals);
        });
        
        // Initial calculation
        updateTotals();
    });
</script>
{% endblock %}