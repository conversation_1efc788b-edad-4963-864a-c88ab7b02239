{% extends 'base.html' %}

{% block title %}Create Supply Request - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">Home</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{% url 'request_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Requests</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Create Request</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <h1 class="text-3xl font-bold text-gray-900">Create Supply Request</h1>
        <p class="mt-2 text-gray-600">Submit a new supply request for your department</p>
    </div>

    <!-- Request Form -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <form hx-post="{% url 'create_request' %}" 
              hx-target="#form-container" 
              hx-swap="outerHTML"
              class="space-y-6 p-6">
            {% csrf_token %}
            
            <div id="form-container">
                <!-- Request Details Section -->
                <div class="border-b border-gray-200 pb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Request Details</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="{{ form.justification.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.justification.label }}
                            </label>
                            {{ form.justification }}
                            {% if form.justification.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.justification.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.required_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.required_date.label }}
                            </label>
                            {{ form.required_date }}
                            {% if form.required_date.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {{ form.required_date.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.notes.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Request Items Section -->
                <div class="pt-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900">Request Items</h2>
                        <button type="button" 
                                hx-get="{% url 'add_request_item' %}"
                                hx-target="#request-items"
                                hx-swap="beforeend"
                                hx-vals="js:{total_forms: document.querySelector('input[name=items-TOTAL_FORMS]').value}"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                onclick="updateFormCount()"
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Item
                        </button>
                    </div>
                    
                    <div id="request-items" class="space-y-4">
                        {{ formset.management_form }}
                        {% for form in formset %}
                            {% include 'requests/partials/request_item_form.html' with form=form form_index=forloop.counter0 %}
                        {% endfor %}
                    </div>
                    
                    {% if formset.non_form_errors %}
                        <div class="mt-4 text-sm text-red-600">
                            {{ formset.non_form_errors }}
                        </div>
                    {% endif %}
                </div>

                <!-- Request Summary -->
                <div class="pt-6">
                    {% include 'requests/partials/request_summary.html' %}
                </div>

                <!-- Submit Section -->
                <div class="pt-6 border-t border-gray-200">
                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'request_list' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span class="htmx-indicator">
                                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                            Create Request
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Available Items Reference -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Items</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for category in categories %}
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-gray-900 mb-2">{{ category.name }}</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        {% for item in category.supplyitem_set.all %}
                            {% if item.is_active and item.current_stock > 0 %}
                                <li class="flex justify-between">
                                    <span>{{ item.name }}</span>
                                    <span class="{% if item.is_low_stock %}text-red-600{% else %}text-green-600{% endif %}">
                                        {{ item.current_stock }} {{ item.unit_of_measure }}
                                    </span>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
    // Handle form submission success
    document.body.addEventListener('requestCreated', function() {
        window.location.href = "{% url 'request_list' %}";
    });
    
    // Update form count before adding new item
    function updateFormCount() {
        const forms = document.querySelectorAll('#request-items .request-item-form');
        const totalFormsInput = document.querySelector('input[name="items-TOTAL_FORMS"]');
        if (totalFormsInput) {
            totalFormsInput.value = forms.length;
        }
    }
    
    // Update form indices when adding/removing items
    function updateFormIndices() {
        const forms = document.querySelectorAll('#request-items .request-item-form');
        forms.forEach((form, index) => {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/items-\d+-/, `items-${index}-`);
                }
                if (input.id) {
                    input.id = input.id.replace(/id_items-\d+-/, `id_items-${index}-`);
                }
            });
        });
        
        // Update total forms count
        const totalFormsInput = document.querySelector('input[name="items-TOTAL_FORMS"]');
        if (totalFormsInput) {
            totalFormsInput.value = forms.length;
        }
    }
    
    // Handle item removal
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-item')) {
            e.target.closest('.request-item-form').remove();
            updateFormIndices();
        }
    });
    
    // Handle HTMX after request to update form indices
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.elt.getAttribute('hx-get') === "{% url 'add_request_item' %}") {
            updateFormIndices();
        }
    });
</script>
{% endblock %}