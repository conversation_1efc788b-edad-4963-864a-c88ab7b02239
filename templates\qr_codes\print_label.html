{% extends 'base.html' %}
{% load static %}

{% block title %}Print QR Label - {{ qr_code.code_id }}{% endblock %}

{% block extra_head %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-area, .print-area * {
            visibility: visible;
        }
        .print-area {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
        .print-label {
            page-break-inside: avoid;
            margin: 0;
            padding: 20px;
            border: 2px solid #000;
        }
    }
    
    .print-label {
        width: 4in;
        height: 3in;
        border: 2px solid #333;
        padding: 15px;
        margin: 20px auto;
        background: white;
        font-family: Arial, sans-serif;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header (No Print) -->
        <div class="mb-8 no-print">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Print QR Code Label</h1>
                    <p class="mt-2 text-gray-600">{{ qr_code.code_id }}</p>
                </div>
                <div class="flex space-x-4">
                    <button onclick="window.print()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-print mr-2"></i>
                        Print Label
                    </button>
                    <a href="{% url 'qr_code_detail' qr_code.code_id %}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </a>
                </div>
            </div>
        </div>

        <!-- Print Instructions (No Print) -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8 no-print">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Printing Instructions</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Use standard 4" x 3" label paper for best results</li>
                            <li>Ensure your printer is set to actual size (no scaling)</li>
                            <li>Use high quality print settings for clear QR code scanning</li>
                            <li>Test scan the printed QR code before applying to items</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Printable Label Area -->
        <div class="print-area">
            <div class="print-label">
                <!-- Header -->
                <div class="text-center mb-3">
                    <h2 class="text-lg font-bold text-gray-900">JHCSC Supply Item</h2>
                    <div class="text-xs text-gray-600">{{ print_date|date:"M d, Y" }}</div>
                </div>

                <!-- QR Code and Item Info -->
                <div class="flex items-start space-x-4">
                    <!-- QR Code -->
                    <div class="flex-shrink-0">
                        {% if qr_code.qr_image %}
                            <img src="{{ qr_code.qr_image.url }}" 
                                 alt="QR Code" 
                                 class="w-20 h-20 border border-gray-300">
                        {% endif %}
                        <div class="text-center mt-1">
                            <div class="text-xs font-mono text-gray-700">{{ qr_code.code_id }}</div>
                        </div>
                    </div>

                    <!-- Item Details -->
                    <div class="flex-1 min-w-0">
                        <div class="space-y-1">
                            <div>
                                <div class="text-xs font-medium text-gray-500">Item:</div>
                                <div class="text-sm font-bold text-gray-900 truncate">{{ qr_code.supply_item.name }}</div>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-gray-500">Code:</div>
                                <div class="text-xs font-mono text-gray-900">{{ qr_code.supply_item.item_code }}</div>
                            </div>
                            <div>
                                <div class="text-xs font-medium text-gray-500">Category:</div>
                                <div class="text-xs text-gray-900">{{ qr_code.supply_item.category.name }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Request Information -->
                <div class="mt-3 pt-2 border-t border-gray-300">
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div>
                            <span class="font-medium text-gray-500">Request:</span>
                            <span class="text-gray-900 font-mono">{{ qr_code.request_item.request.request_id }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">Dept:</span>
                            <span class="text-gray-900">{{ qr_code.request_item.request.department|truncatechars:15 }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">Qty:</span>
                            <span class="text-gray-900">{{ qr_code.request_item.quantity_approved|default:qr_code.request_item.quantity_requested }} {{ qr_code.supply_item.unit_of_measure }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">Date:</span>
                            <span class="text-gray-900">{{ qr_code.generated_date|date:"m/d/Y" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-3 pt-2 border-t border-gray-300 text-center">
                    <div class="text-xs text-gray-600">
                        Scan for item tracking and verification
                    </div>
                </div>
            </div>
        </div>

        <!-- Multiple Labels Option (No Print) -->
        <div class="mt-8 no-print">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Print Multiple Labels</h3>
                <p class="text-sm text-gray-600 mb-4">
                    If you need multiple copies of this label, you can print multiple copies using your browser's print dialog.
                </p>
                
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="multipleCopies" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">Print multiple copies</span>
                    </label>
                    <select id="copyCount" class="disabled:opacity-50 border-gray-300 rounded-md text-sm" disabled>
                        <option value="2">2 copies</option>
                        <option value="3">3 copies</option>
                        <option value="4">4 copies</option>
                        <option value="5">5 copies</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Label Preview (No Print) -->
        <div class="mt-8 no-print">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Label Preview</h3>
                <p class="text-sm text-gray-600 mb-4">
                    This is how your label will appear when printed. Make sure all information is correct before printing.
                </p>
                
                <!-- Preview of the label (smaller version) -->
                <div class="border-2 border-gray-300 p-4 bg-white" style="width: 300px; height: 225px; transform: scale(0.75); transform-origin: top left;">
                    <div class="text-center mb-2">
                        <h4 class="text-sm font-bold">JHCSC Supply Item</h4>
                        <div class="text-xs text-gray-600">{{ print_date|date:"M d, Y" }}</div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            {% if qr_code.qr_image %}
                                <img src="{{ qr_code.qr_image.url }}" alt="QR Code" class="w-16 h-16 border">
                            {% endif %}
                            <div class="text-center mt-1">
                                <div class="text-xs font-mono">{{ qr_code.code_id|truncatechars:12 }}</div>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="text-xs space-y-1">
                                <div><strong>{{ qr_code.supply_item.name|truncatechars:25 }}</strong></div>
                                <div>{{ qr_code.supply_item.item_code }}</div>
                                <div>{{ qr_code.supply_item.category.name }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const multipleCopiesCheckbox = document.getElementById('multipleCopies');
    const copyCountSelect = document.getElementById('copyCount');
    
    multipleCopiesCheckbox.addEventListener('change', function() {
        copyCountSelect.disabled = !this.checked;
    });
});
</script>
{% endblock %}