{% extends 'base.html' %}

{% block title %}Reject Request {{ supply_request.request_id }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">Home</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{% url 'pending_requests_dashboard' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Pending Requests</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Reject {{ supply_request.request_id }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Reject Request {{ supply_request.request_id }}</h1>
                <p class="mt-2 text-gray-600">Provide a reason for rejecting this supply request</p>
            </div>
        </div>
    </div>

    <!-- Warning Notice -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Request Rejection</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>This action will permanently reject the supply request. The requester will be notified of the rejection and the reason provided.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Request Summary -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Request Summary</h2>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                <div>
                    <span class="font-medium text-gray-700">Requester:</span>
                    <span class="text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Department:</span>
                    <span class="text-gray-900">{{ supply_request.department }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Requested Date:</span>
                    <span class="text-gray-900">{{ supply_request.requested_date|date:"M d, Y g:i A" }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Required Date:</span>
                    <span class="text-gray-900">{{ supply_request.required_date|date:"M d, Y" }}</span>
                </div>
            </div>
            <div>
                <span class="font-medium text-gray-700">Justification:</span>
                <p class="text-gray-900 mt-1">{{ supply_request.justification }}</p>
            </div>
            <div class="mt-4">
                <span class="font-medium text-gray-700">Total Items:</span>
                <span class="text-gray-900">{{ supply_request.total_items }} item{{ supply_request.total_items|pluralize }}</span>
                <span class="ml-4 font-medium text-gray-700">Estimated Cost:</span>
                <span class="text-gray-900">${{ supply_request.total_cost }}</span>
            </div>
        </div>
    </div>

    <!-- Rejection Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Rejection Details</h2>
            </div>
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label for="{{ form.rejection_reason.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.rejection_reason.label }}
                    </label>
                    {{ form.rejection_reason }}
                    {% if form.rejection_reason.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.rejection_reason.help_text }}</p>
                    {% endif %}
                    {% if form.rejection_reason.errors %}
                        <div class="text-red-600 text-sm mt-1">
                            {{ form.rejection_reason.errors|join:", " }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="flex items-center">
                    {{ form.notify_requester }}
                    <label for="{{ form.notify_requester.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        {{ form.notify_requester.label }}
                    </label>
                </div>
            </div>
        </div>

        <!-- Common Rejection Reasons (Quick Select) -->
        <div class="bg-gray-50 rounded-lg p-4">
            <h3 class="text-sm font-medium text-gray-700 mb-3">Common Rejection Reasons (Click to use)</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <button type="button" onclick="setRejectionReason('Insufficient budget allocation for requested items.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Insufficient budget allocation
                </button>
                <button type="button" onclick="setRejectionReason('Requested items are not available or discontinued.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Items not available
                </button>
                <button type="button" onclick="setRejectionReason('Request does not meet departmental approval criteria.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Does not meet criteria
                </button>
                <button type="button" onclick="setRejectionReason('Insufficient justification provided for the requested items.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Insufficient justification
                </button>
                <button type="button" onclick="setRejectionReason('Similar items were recently provided to the department.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Recently provided
                </button>
                <button type="button" onclick="setRejectionReason('Request requires additional documentation or approval.')" 
                        class="text-left px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Additional documentation needed
                </button>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'review_request' supply_request.request_id %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Reject Request
            </button>
        </div>
    </form>
</div>

<script>
    function setRejectionReason(reason) {
        document.getElementById('{{ form.rejection_reason.id_for_label }}').value = reason;
    }
</script>
{% endblock %}