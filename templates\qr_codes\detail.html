{% extends 'base.html' %}
{% load static %}

{% block title %}QR Code {{ qr_code.code_id }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">QR Code Details</h1>
                    <p class="mt-2 text-gray-600">{{ qr_code.code_id }}</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{% url 'qr_code_print_label' qr_code.code_id %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-print mr-2"></i>
                        Print Label
                    </a>
                    {% if user.userprofile.role in 'gso_staff,admin' %}
                        <a href="{% url 'regenerate_qr_code' qr_code.code_id %}" 
                           class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-redo mr-2"></i>
                            Regenerate
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- QR Code Image and Basic Info -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">QR Code</h2>
                
                <div class="text-center mb-6">
                    {% if qr_code.qr_image %}
                        <img src="{{ qr_code.qr_image.url }}" 
                             alt="QR Code {{ qr_code.code_id }}" 
                             class="mx-auto border border-gray-300 rounded-lg"
                             style="max-width: 200px;">
                    {% else %}
                        <div class="w-48 h-48 mx-auto bg-gray-100 border border-gray-300 rounded-lg flex items-center justify-center">
                            <i class="fas fa-qrcode text-gray-400 text-6xl"></i>
                        </div>
                    {% endif %}
                </div>

                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Code ID:</span>
                        <span class="text-sm text-gray-900 font-mono">{{ qr_code.code_id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Generated:</span>
                        <span class="text-sm text-gray-900">{{ qr_code.generated_date|date:"M d, Y H:i" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm font-medium text-gray-500">Status:</span>
                        <span class="text-sm">
                            {% if qr_code.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Inactive
                                </span>
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Scan Activity -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Scan Activity</h2>
                
                <div class="space-y-4">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-full">
                                <i class="fas fa-eye text-blue-600"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-800">Total Scans</p>
                                <p class="text-2xl font-bold text-blue-900">{{ qr_code.scanned_count }}</p>
                            </div>
                        </div>
                    </div>

                    {% if qr_code.last_scanned %}
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Last Scanned:</span>
                                <span class="text-sm text-gray-900">{{ qr_code.last_scanned|date:"M d, Y H:i" }}</span>
                            </div>
                            {% if qr_code.last_scanned_by %}
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-500">Scanned By:</span>
                                    <span class="text-sm text-gray-900">{{ qr_code.last_scanned_by.get_full_name|default:qr_code.last_scanned_by.username }}</span>
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-eye-slash text-gray-400 text-3xl mb-2"></i>
                            <p class="text-sm text-gray-500">This QR code has never been scanned</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Item Information -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Item Information</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Item Name</label>
                            <p class="text-lg font-semibold text-gray-900">{{ qr_code.supply_item.name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Item Code</label>
                            <p class="text-sm text-gray-900 font-mono">{{ qr_code.supply_item.item_code }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Category</label>
                            <p class="text-sm text-gray-900">{{ qr_code.supply_item.category.name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Unit of Measure</label>
                            <p class="text-sm text-gray-900">{{ qr_code.supply_item.unit_of_measure }}</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Current Stock</label>
                            <p class="text-sm text-gray-900">{{ qr_code.supply_item.current_stock }} units</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Unit Cost</label>
                            <p class="text-sm text-gray-900">₱{{ qr_code.supply_item.unit_cost }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Supplier</label>
                            <p class="text-sm text-gray-900">{{ qr_code.supply_item.supplier }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Stock Status</label>
                            {% if qr_code.supply_item.is_low_stock %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Low Stock
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    In Stock
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Information -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Request Information</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Request ID</label>
                            <p class="text-sm text-gray-900 font-mono">
                                <a href="{% url 'request_detail' qr_code.request_item.request.request_id %}" 
                                   class="text-blue-600 hover:text-blue-800">
                                    {{ qr_code.request_item.request.request_id }}
                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Requester</label>
                            <p class="text-sm text-gray-900">{{ qr_code.request_item.request.requester.get_full_name|default:qr_code.request_item.request.requester.username }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Department</label>
                            <p class="text-sm text-gray-900">{{ qr_code.request_item.request.department }}</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Request Status</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {% if qr_code.request_item.request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif qr_code.request_item.request.status == 'approved' %}bg-blue-100 text-blue-800
                                {% elif qr_code.request_item.request.status == 'released' %}bg-green-100 text-green-800
                                {% elif qr_code.request_item.request.status == 'completed' %}bg-green-100 text-green-800
                                {% elif qr_code.request_item.request.status == 'rejected' %}bg-red-100 text-red-800
                                {% endif %}">
                                {{ qr_code.request_item.request.get_status_display }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Requested Quantity</label>
                            <p class="text-sm text-gray-900">{{ qr_code.request_item.quantity_requested }} {{ qr_code.supply_item.unit_of_measure }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Approved Quantity</label>
                            <p class="text-sm text-gray-900">
                                {% if qr_code.request_item.quantity_approved %}
                                    {{ qr_code.request_item.quantity_approved }} {{ qr_code.supply_item.unit_of_measure }}
                                {% else %}
                                    Not yet approved
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QR Code Data -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">QR Code Data</h2>
                <p class="text-sm text-gray-600">Technical information encoded in the QR code</p>
            </div>
            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ qr_data|pprint }}</pre>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-8 flex justify-center space-x-4">
            <a href="{% url 'qr_code_management' %}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to QR Codes
            </a>
        </div>
    </div>
</div>
{% endblock %}