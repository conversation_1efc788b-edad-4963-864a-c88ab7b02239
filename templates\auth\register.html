{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Smart Supply Management{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{% url 'login' %}" class="font-medium text-blue-600 hover:text-blue-500">
                    sign in to existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">First Name</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Last Name</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.username.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">Email Address</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700">Role</label>
                    {{ form.role }}
                    {% if form.role.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.role.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                    {{ form.department }}
                    {% if form.department.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.department.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone Number (Optional)</label>
                    {{ form.phone_number }}
                    {% if form.phone_number.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone_number.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">Password</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.password1.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.password2.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                {{ form.non_field_errors.0 }}
                            </h3>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-green-500 group-hover:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}