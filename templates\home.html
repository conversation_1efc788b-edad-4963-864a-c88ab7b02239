{% extends 'base.html' %}

{% block title %}Smart Supply Management System{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="relative hero-bg overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
            <!-- Logo/Icon -->
            <div class="flex justify-center mb-8">
                <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-6">
                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                    </svg>
                </div>
            </div>
            
            <!-- Main Heading -->
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Smart Supply
                <span class="block text-primary-200">Management</span>
            </h1>
            
            <!-- Subtitle -->
            <p class="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto">
                Streamline your inventory, track supplies in real-time, and optimize your supply chain with our intelligent management system.
            </p>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button 
                    onclick="showNotification('Welcome! Getting started with Smart Supply Management...', 'success')"
                    class="bg-white text-primary-700 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-50 hover:scale-105 transition-all duration-200 shadow-lg touch-target">
                    Get Started
                </button>
                <button 
                    onclick="document.querySelector('.py-20.bg-gray-50').scrollIntoView({behavior: 'smooth'})"
                    class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-700 hover:scale-105 transition-all duration-200 touch-target">
                    Learn More
                </button>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
        </svg>
    </div>
</div>

<!-- Features Section -->
<div class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Powerful Features for Modern Supply Management
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to manage your supplies efficiently, from inventory tracking to automated reporting.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1: QR Code Tracking -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-primary-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">QR Code Tracking</h3>
                <p class="text-gray-600 mb-6">
                    Generate and scan QR codes for instant item identification and tracking. Perfect for mobile inventory management.
                </p>
                <button 
                    onclick="showNotification('QR Code Tracking: Generate and scan QR codes for instant item identification!', 'info')"
                    class="flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors cursor-pointer group">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>
            
            <!-- Feature 2: Real-time Analytics -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-green-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2 2z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Real-time Analytics</h3>
                <p class="text-gray-600 mb-6">
                    Get instant insights into your inventory levels, usage patterns, and supply chain performance with live dashboards.
                </p>
                <div class="flex items-center text-primary-600 font-medium">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
            
            <!-- Feature 3: Automated Reports -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-purple-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Automated Reports</h3>
                <p class="text-gray-600 mb-6">
                    Generate comprehensive PDF reports automatically. Track usage, monitor stock levels, and analyze trends effortlessly.
                </p>
                <div class="flex items-center text-primary-600 font-medium">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
            
            <!-- Feature 4: Mobile Optimized -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-blue-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Mobile Optimized</h3>
                <p class="text-gray-600 mb-6">
                    Access your supply management system anywhere, anytime. Fully responsive design optimized for mobile devices.
                </p>
                <div class="flex items-center text-primary-600 font-medium">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
            
            <!-- Feature 5: Secure Access -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-red-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Secure Access</h3>
                <p class="text-gray-600 mb-6">
                    Enterprise-grade security with role-based access control, secure authentication, and data encryption.
                </p>
                <div class="flex items-center text-primary-600 font-medium">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
            
            <!-- Feature 6: Integration Ready -->
            <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div class="bg-yellow-100 rounded-lg p-3 w-fit mb-6">
                    <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Integration Ready</h3>
                <p class="text-gray-600 mb-6">
                    Seamlessly integrate with existing systems and workflows. API-first design for maximum flexibility.
                </p>
                <div class="flex items-center text-primary-600 font-medium">
                    <span>Learn more</span>
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="py-20 bg-primary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Trusted by Organizations Worldwide
            </h2>
            <p class="text-xl text-primary-200">
                Join thousands of businesses optimizing their supply chains
            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-white mb-2" x-data="{ count: 0 }" x-init="setTimeout(() => { let interval = setInterval(() => { count++; if(count >= 1000) clearInterval(interval); }, 2); }, 500)">
                    <span x-text="count + '+'"></span>
                </div>
                <div class="text-primary-200 font-medium">Active Users</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-white mb-2" x-data="{ count: 0 }" x-init="setTimeout(() => { let interval = setInterval(() => { count++; if(count >= 50) clearInterval(interval); }, 20); }, 700)">
                    <span x-text="count + '+'"></span>
                </div>
                <div class="text-primary-200 font-medium">Organizations</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-white mb-2" x-data="{ count: 0 }" x-init="setTimeout(() => { let interval = setInterval(() => { count += 100; if(count >= 10000) clearInterval(interval); }, 1); }, 900)">
                    <span x-text="count.toLocaleString() + '+'"></span>
                </div>
                <div class="text-primary-200 font-medium">Items Tracked</div>
            </div>
            
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-white mb-2">
                    <span>99.9%</span>
                </div>
                <div class="text-primary-200 font-medium">Uptime</div>
            </div>
        </div>
    </div>
</div>

<!-- Demo Section -->
<div class="py-20 bg-gradient-to-r from-gray-50 to-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                See It In Action
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the power of smart supply management with our interactive demo
            </p>
        </div>
        
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div class="bg-gray-800 px-6 py-4 flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="ml-4 text-gray-300 text-sm font-mono">Smart Supply Dashboard</div>
            </div>
            
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Interactive Dashboard</h3>
                        <p class="text-gray-600 mb-6">
                            Monitor your entire supply chain from a single, intuitive dashboard. Track inventory levels, 
                            view real-time analytics, and manage supplies with ease.
                        </p>
                        
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">Real-time inventory tracking</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">Automated low-stock alerts</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                                <span class="text-gray-700">Usage pattern analysis</span>
                            </div>
                        </div>
                        
                        <button 
                            onclick="showNotification('Demo mode activated! Explore the interactive dashboard.', 'info')"
                            class="mt-6 bg-primary-600 hover:bg-primary-700 hover:scale-105 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200">
                            Try Demo
                        </button>
                    </div>
                    
                    <div class="relative">
                        <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-6">
                            <!-- Mock Dashboard -->
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <h4 class="font-semibold text-gray-800">Inventory Overview</h4>
                                    <span class="text-sm text-gray-500">Last updated: 2 min ago</span>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 text-center">
                                        <div class="text-2xl font-bold text-green-600">1,247</div>
                                        <div class="text-sm text-gray-600">Items in Stock</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 text-center">
                                        <div class="text-2xl font-bold text-yellow-600">23</div>
                                        <div class="text-sm text-gray-600">Low Stock</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 text-center">
                                        <div class="text-2xl font-bold text-red-600">5</div>
                                        <div class="text-sm text-gray-600">Out of Stock</div>
                                    </div>
                                </div>
                                
                                <div class="bg-white rounded-lg p-4">
                                    <h5 class="font-medium text-gray-800 mb-3">Recent Activity</h5>
                                    <div class="space-y-2">
                                        <div class="flex items-center text-sm">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                            <span class="text-gray-600">Office Supplies restocked</span>
                                        </div>
                                        <div class="flex items-center text-sm">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                            <span class="text-gray-600">QR code generated for Item #1234</span>
                                        </div>
                                        <div class="flex items-center text-sm">
                                            <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                                            <span class="text-gray-600">Low stock alert: Printer Paper</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="py-20 bg-white">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Ready to Transform Your Supply Management?
        </h2>
        <p class="text-xl text-gray-600 mb-8">
            Start your journey towards more efficient, transparent, and intelligent supply chain management today.
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button 
                onclick="showNotification('Free trial starting soon! We will contact you with setup instructions.', 'success')"
                class="bg-primary-600 hover:bg-primary-700 hover:scale-105 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 shadow-lg touch-target">
                Start Free Trial
            </button>
            <button 
                onclick="showNotification('Demo scheduled! Our team will reach out to arrange a personalized demonstration.', 'info')"
                class="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white hover:scale-105 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 touch-target">
                Schedule Demo
            </button>
        </div>
        
        <p class="text-sm text-gray-500 mt-6">
            No credit card required • 30-day free trial • Cancel anytime
        </p>
    </div>
</div>

<!-- Footer -->
<footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="col-span-1 md:col-span-2">
                <div class="flex items-center mb-4">
                    <svg class="w-8 h-8 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                    </svg>
                    <span class="text-xl font-bold">Smart Supply</span>
                </div>
                <p class="text-gray-400 mb-6 max-w-md">
                    Empowering organizations with intelligent supply chain management solutions that drive efficiency and growth.
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold mb-4">Product</h3>
                <ul class="space-y-2 text-gray-400">
                    <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Pricing</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">API</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Integrations</a></li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold mb-4">Support</h3>
                <ul class="space-y-2 text-gray-400">
                    <li><a href="#" class="hover:text-white transition-colors">Documentation</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
                </ul>
            </div>
        </div>
        
        <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-400 text-sm">
                © 2025 Smart Supply Management System. All rights reserved.
            </p>
            <div class="flex space-x-6 mt-4 md:mt-0">
                <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
                <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
                <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
        </div>
    </div>
</footer>
{% endblock %}

{% block extra_scripts %}
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add scroll effect to hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.bg-gradient-to-br');
        if (hero) {
            hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe feature cards
    document.querySelectorAll('.bg-white.rounded-xl').forEach(card => {
        observer.observe(card);
    });
    
    // Add some interactive hover effects
    document.querySelectorAll('.bg-white.rounded-xl').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>

<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.6s ease-out forwards;
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }
    
    ::-webkit-scrollbar-track {
        background: #f1f5f9;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #3b82f6;
        border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #2563eb;
    }
    
    /* Smooth transitions for interactive elements */
    .transition-all {
        transition: all 0.3s ease;
    }
    
    /* Hero background animation */
    .hero-bg {
        background: linear-gradient(-45deg, #3b82f6, #2563eb, #1d4ed8, #1e40af);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }
    
    @keyframes gradientShift {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }
</style>
{% endblock %}