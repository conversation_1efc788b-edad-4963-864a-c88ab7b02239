#!/usr/bin/env python
"""
Verification script for Task 1: Set up project foundation and dependencies
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.conf import settings
from django.core.management import execute_from_command_line

def check_packages():
    """Verify all required Python packages are available"""
    print("🔍 Checking Python packages...")
    
    try:
        import qrcode
        print("  ✅ qrcode library: Available")
    except ImportError:
        print("  ❌ qrcode library: Missing")
        return False
    
    try:
        import PIL
        print("  ✅ Pillow (PIL): Available")
    except ImportError:
        print("  ❌ Pillow (PIL): Missing")
        return False
    
    try:
        import reportlab
        print("  ✅ ReportLab: Available")
    except ImportError:
        print("  ❌ ReportLab: Missing")
        return False
    
    try:
        import weasyprint
        print("  ✅ WeasyPrint: Available")
    except (ImportError, OSError) as e:
        print("  ⚠️  WeasyPrint: Not available (fallback to ReportLab configured)")
        print(f"     Note: {str(e)[:100]}...")
    
    return True

def check_django_settings():
    """Verify Django settings are properly configured"""
    print("\n🔍 Checking Django settings...")
    
    # Media files configuration
    if hasattr(settings, 'MEDIA_URL') and hasattr(settings, 'MEDIA_ROOT'):
        print("  ✅ Media files: Configured")
    else:
        print("  ❌ Media files: Not configured")
        return False
    
    # Static files configuration
    if hasattr(settings, 'STATIC_URL') and hasattr(settings, 'STATICFILES_DIRS'):
        print("  ✅ Static files: Configured")
    else:
        print("  ❌ Static files: Not configured")
        return False
    
    # Security configurations (Requirements 8.1, 8.2, 8.6)
    security_checks = [
        ('SECURE_CONTENT_TYPE_NOSNIFF', True),
        ('SECURE_BROWSER_XSS_FILTER', True),
        ('X_FRAME_OPTIONS', 'DENY'),
        ('SESSION_COOKIE_HTTPONLY', True),
        ('CSRF_COOKIE_HTTPONLY', True),
        ('SESSION_COOKIE_AGE', 3600),
    ]
    
    security_passed = True
    for setting_name, expected_value in security_checks:
        if hasattr(settings, setting_name):
            actual_value = getattr(settings, setting_name)
            if actual_value == expected_value:
                print(f"  ✅ {setting_name}: {actual_value}")
            else:
                print(f"  ⚠️  {setting_name}: {actual_value} (expected {expected_value})")
        else:
            print(f"  ❌ {setting_name}: Not configured")
            security_passed = False
    
    # Logging configuration
    if hasattr(settings, 'LOGGING'):
        print("  ✅ Logging: Configured")
    else:
        print("  ❌ Logging: Not configured")
        return False
    
    return security_passed

def check_base_template():
    """Verify base template includes required frontend libraries"""
    print("\n🔍 Checking base template...")
    
    template_path = Path('templates/base.html')
    if not template_path.exists():
        print("  ❌ Base template: Not found")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_libraries = [
        ('Tailwind CSS', 'tailwindcss.com'),
        ('HTMX', 'htmx.org'),
        ('Alpine.js', 'alpinejs'),
        ('Unpoly', 'unpoly'),
        ('QR Code Scanner', 'html5-qrcode'),
    ]
    
    all_found = True
    for lib_name, lib_identifier in required_libraries:
        if lib_identifier in content:
            print(f"  ✅ {lib_name}: Included")
        else:
            print(f"  ❌ {lib_name}: Missing")
            all_found = False
    
    # Check for mobile responsiveness
    if 'viewport' in content and 'width=device-width' in content:
        print("  ✅ Mobile viewport: Configured")
    else:
        print("  ❌ Mobile viewport: Not configured")
        all_found = False
    
    # Check for CSRF token handling
    if 'csrf_token' in content:
        print("  ✅ CSRF token handling: Configured")
    else:
        print("  ❌ CSRF token handling: Missing")
        all_found = False
    
    return all_found

def check_directories():
    """Verify required directories exist"""
    print("\n🔍 Checking directory structure...")
    
    required_dirs = [
        'media',
        'static',
        'staticfiles',
        'templates',
        'logs',
    ]
    
    all_exist = True
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"  ✅ {dir_name}/: Exists")
        else:
            print(f"  ❌ {dir_name}/: Missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all verification checks"""
    print("🚀 Verifying Task 1: Set up project foundation and dependencies\n")
    
    checks = [
        ("Python packages", check_packages),
        ("Django settings", check_django_settings),
        ("Base template", check_base_template),
        ("Directory structure", check_directories),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"  ❌ Error during {check_name} check: {e}")
            # Don't fail for WeasyPrint issues on Windows
            if "weasyprint" not in str(e).lower() and "libgobject" not in str(e).lower():
                all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All foundation setup checks PASSED!")
        print("✅ Task 1 requirements have been successfully implemented:")
        print("   • Python packages installed and configured")
        print("   • Django settings updated for media, static, and security")
        print("   • Base template configured with Tailwind, HTMX, Alpine.js, and Unpoly")
        print("   • Mobile-responsive design foundation ready")
        print("   • Security configurations implemented (Requirements 8.1, 8.2, 8.6)")
    else:
        print("❌ Some foundation setup checks FAILED!")
        print("Please review the issues above and fix them.")
        sys.exit(1)

if __name__ == '__main__':
    main()