{% extends 'base.html' %}
{% load static %}

{% block title %}User Management - Smart Supply Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
        <p class="mt-2 text-sm text-gray-600">
            Manage user accounts, roles, and permissions for the Smart Supply Management System.
        </p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ active_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Inactive Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ inactive_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">All Users</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Click on a user to view details or manage their account.
            </p>
        </div>
        
        <ul class="divide-y divide-gray-200">
            {% for user in users %}
                <li>
                    <div class="px-4 py-4 flex items-center justify-between hover:bg-gray-50">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">
                                        {{ user.first_name.0|default:user.username.0|upper }}{{ user.last_name.0|upper }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ user.get_full_name|default:user.username }}
                                    </div>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if user.userprofile.role == 'admin' %}bg-purple-100 text-purple-800
                                        {% elif user.userprofile.role == 'gso_staff' %}bg-blue-100 text-blue-800
                                        {% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ user.userprofile.get_role_display }}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ user.email }} • {{ user.userprofile.department }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <!-- Status Badge -->
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.userprofile.is_active %}bg-green-100 text-green-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {% if user.userprofile.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                            
                            <!-- Actions -->
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'user_detail' user.id %}" 
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                    View Details
                                </a>
                                
                                <button onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ user.userprofile.is_active|yesno:'true,false' }})"
                                        class="text-sm font-medium
                                        {% if user.userprofile.is_active %}text-red-600 hover:text-red-900
                                        {% else %}text-green-600 hover:text-green-900{% endif %}">
                                    {% if user.userprofile.is_active %}Deactivate{% else %}Activate{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </li>
            {% empty %}
                <li class="px-4 py-8 text-center text-gray-500">
                    No users found.
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<!-- JavaScript for HTMX interactions -->
<script>
function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const message = `Are you sure you want to ${action} user "${username}"?`;
    
    if (confirm(message)) {
        fetch(`/admin/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the page to reflect changes
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating user status.');
        });
    }
}
</script>

{% csrf_token %}
{% endblock %}