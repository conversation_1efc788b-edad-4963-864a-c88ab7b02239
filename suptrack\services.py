"""
Services for supply tracking system
"""
import qrcode
import io
import base64
from django.core.files.base import ContentFile
from django.utils import timezone
from django.conf import settings
from .models import QRCode, StockTransaction
import json


class QRCodeService:
    """Service for generating and managing QR codes"""
    
    @staticmethod
    def generate_qr_code(request_item, quantity_released):
        """
        Generate QR code for a released item
        
        Args:
            request_item: RequestItem instance
            quantity_released: Number of items being released
            
        Returns:
            QRCode instance
        """
        # Create QR code data
        qr_data = {
            'item_id': request_item.supply_item.id,
            'item_code': request_item.supply_item.item_code,
            'item_name': request_item.supply_item.name,
            'request_id': request_item.request.request_id,
            'quantity': quantity_released,
            'department': request_item.request.department,
            'released_date': timezone.now().isoformat(),
            'unit_of_measure': request_item.supply_item.unit_of_measure,
            'requester': request_item.request.requester.username,
            'category': request_item.supply_item.category.name
        }
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Medium error correction for better reliability
            box_size=10,
            border=4,
        )
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Save image to memory
        img_io = io.BytesIO()
        img.save(img_io, format='PNG')
        img_io.seek(0)
        
        # Create QRCode instance
        qr_code = QRCode(
            supply_item=request_item.supply_item,
            request_item=request_item
        )
        
        # Save image file
        filename = f"qr_{qr_code.code_id}.png"
        qr_code.qr_image.save(
            filename,
            ContentFile(img_io.getvalue()),
            save=False
        )
        
        qr_code.save()
        return qr_code
    
    @staticmethod
    def regenerate_qr_code(qr_code_instance):
        """
        Regenerate QR code for damaged or lost codes
        
        Args:
            qr_code_instance: Existing QRCode instance
            
        Returns:
            QRCode instance with new image
        """
        # Deactivate old QR code
        qr_code_instance.is_active = False
        qr_code_instance.save()
        
        # Generate new QR code with same data but new ID
        new_qr_code = QRCodeService.generate_qr_code(
            qr_code_instance.request_item, 
            qr_code_instance.request_item.quantity_approved or 1
        )
        
        return new_qr_code
    
    @staticmethod
    def validate_qr_code(qr_data):
        """
        Validate QR code data structure and content
        
        Args:
            qr_data: JSON string or dict containing QR code data
            
        Returns:
            dict: Validation result with success status and data/error
        """
        try:
            if isinstance(qr_data, str):
                data = json.loads(qr_data)
            else:
                data = qr_data
            
            required_fields = ['item_id', 'item_code', 'request_id', 'quantity']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                return {
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }
            
            # Validate data types and values
            try:
                item_id = int(data['item_id'])
                quantity = int(data['quantity'])
                
                if quantity <= 0:
                    return {
                        'success': False,
                        'error': 'Invalid quantity: must be greater than 0'
                    }
                    
            except (ValueError, TypeError):
                return {
                    'success': False,
                    'error': 'Invalid data types in QR code'
                }
            
            return {
                'success': True,
                'data': data
            }
            
        except json.JSONDecodeError:
            return {
                'success': False,
                'error': 'Invalid QR code format'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'QR code validation error: {str(e)}'
            }
    
    @staticmethod
    def verify_qr_code(code_id):
        """
        Verify QR code exists and is active in the system
        
        Args:
            code_id: QR code ID to verify
            
        Returns:
            dict: Verification result with QR code data or error
        """
        try:
            qr_code = QRCode.objects.select_related(
                'supply_item', 'request_item__request'
            ).get(code_id=code_id, is_active=True)
            
            return {
                'success': True,
                'qr_code': qr_code,
                'data': {
                    'code_id': qr_code.code_id,
                    'item_name': qr_code.supply_item.name,
                    'item_code': qr_code.supply_item.item_code,
                    'request_id': qr_code.request_item.request.request_id,
                    'department': qr_code.request_item.request.department,
                    'generated_date': qr_code.generated_date.isoformat(),
                    'scan_count': qr_code.scanned_count,
                    'last_scanned': qr_code.last_scanned.isoformat() if qr_code.last_scanned else None
                }
            }
            
        except QRCode.DoesNotExist:
            return {
                'success': False,
                'error': 'QR code not found or inactive'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'QR code verification error: {str(e)}'
            }
    
    @staticmethod
    def get_qr_code_data(qr_code_instance):
        """
        Extract data from QR code for scanning operations
        
        Args:
            qr_code_instance: QRCode model instance
            
        Returns:
            dict: QR code data for scanning operations
        """
        return {
            'code_id': qr_code_instance.code_id,
            'item_id': qr_code_instance.supply_item.id,
            'item_code': qr_code_instance.supply_item.item_code,
            'item_name': qr_code_instance.supply_item.name,
            'request_id': qr_code_instance.request_item.request.request_id,
            'quantity': qr_code_instance.request_item.quantity_approved or 1,
            'department': qr_code_instance.request_item.request.department,
            'requester': qr_code_instance.request_item.request.requester.username,
            'category': qr_code_instance.supply_item.category.name,
            'unit_of_measure': qr_code_instance.supply_item.unit_of_measure,
            'generated_date': qr_code_instance.generated_date.isoformat(),
            'scan_count': qr_code_instance.scanned_count,
            'last_scanned': qr_code_instance.last_scanned.isoformat() if qr_code_instance.last_scanned else None,
            'is_active': qr_code_instance.is_active
        }
    
    @staticmethod
    def process_qr_scan(code_id, scanned_by, scan_type='general'):
        """
        Process QR code scan and update tracking information
        
        Args:
            code_id: QR code ID that was scanned
            scanned_by: User who scanned the code
            scan_type: Type of scan (general, issuance, return, audit)
            
        Returns:
            dict: Scan processing result
        """
        try:
            qr_code = QRCode.objects.select_related(
                'supply_item', 'request_item__request'
            ).get(code_id=code_id, is_active=True)
            
            # Record the scan
            qr_code.record_scan(scanned_by)
            
            # Get QR code data
            qr_data = QRCodeService.get_qr_code_data(qr_code)
            
            return {
                'success': True,
                'scan_type': scan_type,
                'qr_data': qr_data,
                'message': f'QR code scanned successfully - {qr_code.supply_item.name}'
            }
            
        except QRCode.DoesNotExist:
            return {
                'success': False,
                'error': 'QR code not found or inactive'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'QR scan processing error: {str(e)}'
            }
    
    @staticmethod
    def get_qr_codes_for_request(request_id):
        """
        Get all QR codes associated with a supply request
        
        Args:
            request_id: Supply request ID
            
        Returns:
            QuerySet: QR codes for the request
        """
        return QRCode.objects.filter(
            request_item__request__request_id=request_id,
            is_active=True
        ).select_related('supply_item', 'request_item')
    
    @staticmethod
    def get_qr_codes_for_item(supply_item_id):
        """
        Get all QR codes associated with a supply item
        
        Args:
            supply_item_id: Supply item ID
            
        Returns:
            QuerySet: QR codes for the item
        """
        return QRCode.objects.filter(
            supply_item_id=supply_item_id,
            is_active=True
        ).select_related('supply_item', 'request_item__request')


class InventoryService:
    """Service for inventory management operations"""
    
    @staticmethod
    def validate_stock_availability(request_items):
        """
        Validate if requested quantities are available in stock
        
        Args:
            request_items: List of RequestItem instances
            
        Returns:
            dict: Validation result with availability status and details
        """
        validation_results = []
        all_available = True
        
        for item in request_items:
            available_stock = item.supply_item.current_stock
            requested_qty = item.quantity_requested
            
            is_available = requested_qty <= available_stock
            if not is_available:
                all_available = False
            
            validation_results.append({
                'item': item,
                'requested_quantity': requested_qty,
                'available_stock': available_stock,
                'is_available': is_available,
                'shortage': max(0, requested_qty - available_stock)
            })
        
        return {
            'all_available': all_available,
            'items': validation_results
        }
    
    @staticmethod
    def create_stock_transaction(supply_item, transaction_type, quantity, 
                               performed_by, reference_document='', 
                               notes='', request_item=None):
        """
        Create a stock transaction record
        
        Args:
            supply_item: SupplyItem instance
            transaction_type: Type of transaction ('in', 'out', 'adjustment', etc.)
            quantity: Quantity (positive for in, negative for out)
            performed_by: User who performed the transaction
            reference_document: Reference document number
            notes: Additional notes
            request_item: Related RequestItem if applicable
            
        Returns:
            StockTransaction instance
        """
        transaction = StockTransaction.objects.create(
            supply_item=supply_item,
            transaction_type=transaction_type,
            quantity=quantity,
            reference_document=reference_document,
            performed_by=performed_by,
            notes=notes,
            request_item=request_item
        )
        
        return transaction
    
    @staticmethod
    def process_item_release(request_item, quantity_released, performed_by, notes=''):
        """
        Process the release of approved items
        
        Args:
            request_item: RequestItem instance
            quantity_released: Quantity being released
            performed_by: User performing the release
            notes: Release notes
            
        Returns:
            StockTransaction instance
        """
        # Create outgoing stock transaction
        transaction = InventoryService.create_stock_transaction(
            supply_item=request_item.supply_item,
            transaction_type='out',
            quantity=-quantity_released,  # Negative for outgoing
            performed_by=performed_by,
            reference_document=request_item.request.request_id,
            notes=f'Item release: {notes}' if notes else 'Item release',
            request_item=request_item
        )
        
        return transaction


class NotificationService:
    """Service for handling notifications"""
    
    @staticmethod
    def notify_request_approved(supply_request, approved_by):
        """
        Send notification when request is approved
        
        Args:
            supply_request: SupplyRequest instance
            approved_by: User who approved the request
        """
        # In a real implementation, this would send email/SMS notifications
        # For now, we'll just log the notification
        print(f"NOTIFICATION: Request {supply_request.request_id} approved by {approved_by.username}")
        print(f"Recipient: {supply_request.requester.email}")
        
        # TODO: Implement actual notification sending (email, SMS, etc.)
        pass
    
    @staticmethod
    def notify_request_rejected(supply_request, rejected_by, reason):
        """
        Send notification when request is rejected
        
        Args:
            supply_request: SupplyRequest instance
            rejected_by: User who rejected the request
            reason: Rejection reason
        """
        # In a real implementation, this would send email/SMS notifications
        print(f"NOTIFICATION: Request {supply_request.request_id} rejected by {rejected_by.username}")
        print(f"Recipient: {supply_request.requester.email}")
        print(f"Reason: {reason}")
        
        # TODO: Implement actual notification sending (email, SMS, etc.)
        pass
    
    @staticmethod
    def notify_items_released(supply_request, released_by):
        """
        Send notification when items are released
        
        Args:
            supply_request: SupplyRequest instance
            released_by: User who released the items
        """
        print(f"NOTIFICATION: Items released for request {supply_request.request_id} by {released_by.username}")
        print(f"Recipient: {supply_request.requester.email}")
        
        # TODO: Implement actual notification sending (email, SMS, etc.)
        pass