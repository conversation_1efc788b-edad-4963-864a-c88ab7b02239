INFO 2025-07-23 22:00:56,525 basehttp 27936 16752 "GET / HTTP/1.1" 200 8905
WARNING 2025-07-23 22:00:57,697 log 27936 13236 Not Found: /favicon.ico
WARNING 2025-07-23 22:00:57,697 basehttp 27936 13236 "GET /favicon.ico HTTP/1.1" 404 3332
INFO 2025-07-23 22:14:15,485 basehttp 6936 36348 "GET / HTTP/1.1" 200 36874
INFO 2025-07-23 22:44:09,998 basehttp 34124 400 "GET / HTTP/1.1" 200 36874
WARNING 2025-07-23 22:44:10,603 log 34124 400 Not Found: /favicon.ico
WARNING 2025-07-23 22:44:10,604 basehttp 34124 400 "GET /favicon.ico HTTP/1.1" 404 7457
ERROR 2025-07-23 22:45:52,264 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-23 22:45:52,277 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-23 22:45:52,291 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-23 22:45:52,304 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-23 22:45:52,670 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-23 22:45:52,683 log 6464 32908 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\dev\smartsupply\.venv\Lib\site-packages\django\http\request.py", line 202, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
INFO 2025-07-24 06:40:18,020 autoreload 36016 9520 Watching for file changes with StatReloader
