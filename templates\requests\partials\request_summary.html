<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <h3 class="text-sm font-medium text-blue-800 mb-2">Request Summary</h3>
    <div id="summary-content">
        <div class="text-sm text-blue-700">
            <div class="flex justify-between">
                <span>Total Items:</span>
                <span id="total-items">0</span>
            </div>
            <div class="flex justify-between">
                <span>Estimated Cost:</span>
                <span id="total-cost">$0.00</span>
            </div>
        </div>
    </div>
</div>

<script>
    function updateRequestSummary() {
        let totalItems = 0;
        let totalCost = 0;
        
        const itemForms = document.querySelectorAll('#request-items .request-item-form');
        itemForms.forEach(form => {
            const itemSelect = form.querySelector('select[name*="supply_item"]');
            const quantityInput = form.querySelector('input[name*="quantity_requested"]');
            
            if (itemSelect && itemSelect.value && quantityInput && quantityInput.value) {
                totalItems += 1;
                // Note: Cost calculation would need item price data
            }
        });
        
        document.getElementById('total-items').textContent = totalItems;
        // document.getElementById('total-cost').textContent = `$${totalCost.toFixed(2)}`;
    }
    
    // Update summary when items change
    document.addEventListener('change', function(e) {
        if (e.target.name && (e.target.name.includes('supply_item') || e.target.name.includes('quantity_requested'))) {
            updateRequestSummary();
        }
    });
    
    // Update summary when items are added/removed
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        updateRequestSummary();
    });
</script>