<div class="flex justify-between items-start">
    <div>
        <h5 class="font-medium text-gray-900">{{ item.name }}</h5>
        <p class="text-sm text-gray-600 mt-1">{{ item.description }}</p>
        <div class="flex items-center space-x-4 mt-2 text-sm">
            <span class="text-gray-500">Category: {{ item.category.name }}</span>
            <span class="text-gray-500">Unit: {{ item.unit_of_measure }}</span>
            <span class="text-gray-500">Cost: ${{ item.unit_cost }}</span>
        </div>
    </div>
    <div class="text-right">
        <div class="text-sm font-medium {{ stock_class }}">
            {{ stock_status }}
        </div>
        <div class="text-sm text-gray-600">
            {{ item.current_stock }} available
        </div>
        {% if item.is_low_stock %}
            <div class="text-xs text-red-600 mt-1">
                Below minimum ({{ item.minimum_threshold }})
            </div>
        {% endif %}
    </div>
</div>