{% extends 'base.html' %}

{% block title %}Request Dashboard - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Request Dashboard</h1>
        <p class="mt-2 text-gray-600">Overview of supply request activity and statistics</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.total_requests }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-lg font-medium text-yellow-600">{{ stats.pending_requests }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Approved</dt>
                            <dd class="text-lg font-medium text-green-600">{{ stats.approved_requests }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Rejected</dt>
                            <dd class="text-lg font-medium text-red-600">{{ stats.rejected_requests }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-600">{{ stats.completed_requests }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Requests -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Recent Requests</h2>
            </div>
            <div class="divide-y divide-gray-200">
                {% for request in recent_requests %}
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <a href="{% url 'request_detail' request.request_id %}" 
                                       class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                        {{ request.request_id }}
                                    </a>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        {% if request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif request.status == 'approved' %}bg-green-100 text-green-800
                                        {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                        {% elif request.status == 'released' %}bg-blue-100 text-blue-800
                                        {% elif request.status == 'completed' %}bg-gray-100 text-gray-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </div>
                                <div class="mt-1 text-sm text-gray-600">
                                    {{ request.total_items }} item{{ request.total_items|pluralize }} • {{ request.department }}
                                </div>
                                <div class="mt-1 text-xs text-gray-500">
                                    {{ request.requested_date|date:"M d, Y g:i A" }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="px-6 py-8 text-center">
                        <div class="text-gray-500">
                            <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="mt-2 text-sm text-gray-500">No recent requests</p>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="px-6 py-3 border-t border-gray-200">
                <a href="{% url 'request_list' %}" 
                   class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View all requests →
                </a>
            </div>
        </div>

        <!-- Pending Requests for GSO Staff -->
        {% if user_role == 'gso_staff' or user_role == 'admin' %}
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Pending Approvals</h2>
                </div>
                <div class="divide-y divide-gray-200">
                    {% for request in pending_requests %}
                        <div class="px-6 py-4">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <a href="{% url 'request_detail' request.request_id %}" 
                                           class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                            {{ request.request_id }}
                                        </a>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    </div>
                                    <div class="mt-1 text-sm text-gray-600">
                                        {{ request.requester.get_full_name|default:request.requester.username }} • {{ request.department }}
                                    </div>
                                    <div class="mt-1 text-xs text-gray-500">
                                        {{ request.total_items }} item{{ request.total_items|pluralize }} • {{ request.requested_date|date:"M d, Y" }}
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Approve
                                    </button>
                                    <button class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <div class="px-6 py-8 text-center">
                            <div class="text-gray-500">
                                <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p class="mt-2 text-sm text-gray-500">No pending requests</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <!-- Quick Actions for Department Users -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
                </div>
                <div class="p-6 space-y-4">
                    <a href="{% url 'quick_request' %}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Quick Request
                    </a>
                    <a href="{% url 'create_request' %}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Request
                    </a>
                    <a href="{% url 'request_list' %}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        View All Requests
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Auto-refresh for real-time updates -->
    <div hx-get="{% url 'request_dashboard' %}" 
         hx-trigger="every 30s" 
         hx-target="body" 
         hx-swap="outerHTML"
         class="hidden">
    </div>
</div>
{% endblock %}