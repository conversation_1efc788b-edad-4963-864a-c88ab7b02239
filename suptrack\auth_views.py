from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import PasswordResetView, PasswordResetConfirmView
from django.contrib.auth.models import User
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.utils.decorators import method_decorator
from django.core.exceptions import ValidationError
from django.db import transaction
import logging

from .forms import (
    CustomUserRegistrationForm, 
    CustomLoginForm, 
    CustomPasswordResetForm,
    CustomSetPasswordForm,
    UserProfileForm
)
from .models import UserProfile
from .decorators import admin_required, role_required

logger = logging.getLogger(__name__)


@csrf_protect
def register_view(request):
    """User registration view with role assignment"""
    if request.user.is_authenticated:
        return redirect('home')
    
    if request.method == 'POST':
        form = CustomUserRegistrationForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    user = form.save()
                    messages.success(
                        request, 
                        f'Account created successfully! You can now log in with your credentials.'
                    )
                    logger.info(f"New user registered: {user.username} with role {user.userprofile.role}")
                    return redirect('login')
            except Exception as e:
                logger.error(f"Registration error: {str(e)}")
                messages.error(request, 'An error occurred during registration. Please try again.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomUserRegistrationForm()
    
    return render(request, 'auth/register.html', {'form': form})


@csrf_protect
def login_view(request):
    """Custom login view with session management"""
    if request.user.is_authenticated:
        return redirect('home')
    
    if request.method == 'POST':
        form = CustomLoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            remember_me = form.cleaned_data.get('remember_me', False)
            
            user = authenticate(username=username, password=password)
            if user is not None:
                try:
                    # Check if user profile exists and is active
                    profile = user.userprofile
                    if not profile.is_active:
                        messages.error(request, 'Your account has been deactivated. Please contact an administrator.')
                        return render(request, 'auth/login.html', {'form': form})
                    
                    login(request, user)
                    
                    # Set session expiry based on remember_me
                    if remember_me:
                        request.session.set_expiry(1209600)  # 2 weeks
                    else:
                        request.session.set_expiry(3600)  # 1 hour
                    
                    logger.info(f"User logged in: {user.username} ({profile.get_role_display()})")
                    messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')
                    
                    # Redirect to next parameter or home
                    next_url = request.GET.get('next', 'home')
                    return redirect(next_url)
                    
                except UserProfile.DoesNotExist:
                    messages.error(request, 'User profile not found. Please contact an administrator.')
                    logger.error(f"User {user.username} has no profile")
            else:
                messages.error(request, 'Invalid username or password.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomLoginForm()
    
    return render(request, 'auth/login.html', {'form': form})


@login_required
def logout_view(request):
    """Custom logout view"""
    username = request.user.username
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    logger.info(f"User logged out: {username}")
    return redirect('login')


@method_decorator(csrf_protect, name='dispatch')
class CustomPasswordResetView(PasswordResetView):
    """Custom password reset view"""
    form_class = CustomPasswordResetForm
    template_name = 'auth/password_reset.html'
    email_template_name = 'auth/password_reset_email.html'
    subject_template_name = 'auth/password_reset_subject.txt'
    success_url = reverse_lazy('password_reset_done')
    
    def form_valid(self, form):
        logger.info(f"Password reset requested for email: {form.cleaned_data['email']}")
        return super().form_valid(form)


@method_decorator(csrf_protect, name='dispatch')
class CustomPasswordResetConfirmView(PasswordResetConfirmView):
    """Custom password reset confirm view"""
    form_class = CustomSetPasswordForm
    template_name = 'auth/password_reset_confirm.html'
    success_url = reverse_lazy('password_reset_complete')
    
    def form_valid(self, form):
        logger.info(f"Password reset completed for user: {self.user.username}")
        return super().form_valid(form)


@login_required
def profile_view(request):
    """User profile view and edit"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        messages.error(request, 'User profile not found. Please contact an administrator.')
        return redirect('home')
    
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=profile, user=request.user)
        if form.is_valid():
            try:
                with transaction.atomic():
                    form.save()
                    messages.success(request, 'Profile updated successfully!')
                    logger.info(f"Profile updated for user: {request.user.username}")
                    return redirect('profile')
            except Exception as e:
                logger.error(f"Profile update error: {str(e)}")
                messages.error(request, 'An error occurred while updating your profile.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = UserProfileForm(instance=profile, user=request.user)
    
    context = {
        'form': form,
        'profile': profile,
        'user': request.user
    }
    return render(request, 'auth/profile.html', context)


@login_required
@admin_required
def user_management_view(request):
    """Admin view for managing users"""
    users = User.objects.select_related('userprofile').all().order_by('-date_joined')
    
    context = {
        'users': users,
        'total_users': users.count(),
        'active_users': users.filter(userprofile__is_active=True).count(),
        'inactive_users': users.filter(userprofile__is_active=False).count(),
    }
    return render(request, 'auth/user_management.html', context)


@login_required
@admin_required
@require_http_methods(["POST"])
def toggle_user_status(request, user_id):
    """AJAX endpoint to toggle user active status"""
    try:
        user = get_object_or_404(User, id=user_id)
        profile = user.userprofile
        
        # Prevent admin from deactivating themselves
        if user == request.user:
            return JsonResponse({
                'success': False,
                'message': 'You cannot deactivate your own account.'
            }, status=400)
        
        profile.is_active = not profile.is_active
        profile.save()
        
        action = 'activated' if profile.is_active else 'deactivated'
        logger.info(f"User {user.username} {action} by {request.user.username}")
        
        return JsonResponse({
            'success': True,
            'message': f'User {user.username} has been {action}.',
            'is_active': profile.is_active
        })
        
    except UserProfile.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'User profile not found.'
        }, status=404)
    except Exception as e:
        logger.error(f"Error toggling user status: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while updating user status.'
        }, status=500)


@login_required
@admin_required
def user_detail_view(request, user_id):
    """Admin view for user details"""
    user = get_object_or_404(User, id=user_id)
    try:
        profile = user.userprofile
    except UserProfile.DoesNotExist:
        messages.error(request, 'User profile not found.')
        return redirect('user_management')
    
    # Get user's recent activity (requests, transactions, etc.)
    recent_requests = user.supply_requests.all()[:5]
    recent_transactions = user.stocktransaction_set.all()[:5]
    
    context = {
        'profile_user': user,
        'profile': profile,
        'recent_requests': recent_requests,
        'recent_transactions': recent_transactions,
    }
    return render(request, 'auth/user_detail.html', context)


@login_required
def change_password_view(request):
    """View for users to change their password"""
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password1 = request.POST.get('new_password1')
        new_password2 = request.POST.get('new_password2')
        
        # Validate current password
        if not request.user.check_password(current_password):
            messages.error(request, 'Current password is incorrect.')
            return render(request, 'auth/change_password.html')
        
        # Validate new passwords match
        if new_password1 != new_password2:
            messages.error(request, 'New passwords do not match.')
            return render(request, 'auth/change_password.html')
        
        # Validate password strength (basic validation)
        if len(new_password1) < 8:
            messages.error(request, 'Password must be at least 8 characters long.')
            return render(request, 'auth/change_password.html')
        
        try:
            request.user.set_password(new_password1)
            request.user.save()
            messages.success(request, 'Password changed successfully! Please log in again.')
            logger.info(f"Password changed for user: {request.user.username}")
            logout(request)
            return redirect('login')
        except Exception as e:
            logger.error(f"Password change error: {str(e)}")
            messages.error(request, 'An error occurred while changing your password.')
    
    return render(request, 'auth/change_password.html')


# Password reset done and complete views
def password_reset_done_view(request):
    """Password reset done view"""
    return render(request, 'auth/password_reset_done.html')


def password_reset_complete_view(request):
    """Password reset complete view"""
    return render(request, 'auth/password_reset_complete.html')