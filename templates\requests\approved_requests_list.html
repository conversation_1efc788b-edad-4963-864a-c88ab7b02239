{% extends 'base.html' %}

{% block title %}Approved Requests - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Approved Requests</h1>
                <p class="mt-2 text-gray-600">Manage approved requests ready for item release</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'pending_requests_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Pending Requests
                </a>
                <a href="{% url 'request_dashboard' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Requests</h2>
            <form class="grid grid-cols-1 md:grid-cols-3 gap-4"
                  hx-get="{% url 'approved_requests_list' %}"
                  hx-target="#approved-requests-list"
                  hx-trigger="change, keyup changed delay:500ms from:input[type=text]">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <input type="text" name="department" value="{{ selected_department }}" placeholder="Filter by department..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="flex items-end">
                    <button type="button" 
                            onclick="this.form.reset(); htmx.trigger(this.form, 'change')"
                            class="w-full px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Clear Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Approved Requests List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">
                Approved Requests ({{ page_obj.paginator.count }})
            </h2>
        </div>
        
        <div id="approved-requests-list" class="htmx-indicator-parent">
            <div class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <div class="flex items-center space-x-2">
                    <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600">Loading requests...</span>
                </div>
            </div>
            {% include 'requests/partials/approved_requests_list.html' %}
        </div>
    </div>

    <!-- Auto-refresh for real-time updates -->
    <div hx-get="{% url 'approved_requests_list' %}" 
         hx-trigger="every 60s" 
         hx-target="#approved-requests-list"
         class="hidden">
    </div>
</div>

<script>
    // Handle request updates
    document.body.addEventListener('itemsReleased', function() {
        htmx.trigger('#approved-requests-list', 'refresh');
    });
</script>
{% endblock %}