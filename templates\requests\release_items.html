{% extends 'base.html' %}
{% load form_filters %}

{% block title %}Release Items - Request {{ supply_request.request_id }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">Home</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{% url 'approved_requests_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Approved Requests</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">Release {{ supply_request.request_id }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Release Items - Request {{ supply_request.request_id }}</h1>
                <p class="mt-2 text-gray-600">Process item release and generate QR codes for tracking</p>
            </div>
        </div>
    </div>

    <!-- Release Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Request Summary -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Request Summary</h2>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Requester:</span>
                        <span class="text-gray-900">{{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Department:</span>
                        <span class="text-gray-900">{{ supply_request.department }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Approved Date:</span>
                        <span class="text-gray-900">{{ supply_request.approved_date|date:"M d, Y g:i A" }}</span>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="font-medium text-gray-700">Approved By:</span>
                    <span class="text-gray-900">{{ supply_request.approved_by.get_full_name|default:supply_request.approved_by.username }}</span>
                </div>
            </div>
        </div>

        <!-- Items for Release -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Items for Release</h2>
                <p class="text-sm text-gray-600 mt-1">Adjust release quantities and select items for QR code generation.</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Approved Quantity
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Release Quantity
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Generate QR Code
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit Price
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total Value
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in approved_items %}
                            {% if item.quantity_approved and item.quantity_approved > 0 %}
                                {% with release_field='release_quantity_'|add:item.id|stringformat:"s" qr_field='generate_qr_'|add:item.id|stringformat:"s" %}
                                    <tr>
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                            <div class="text-sm text-gray-500">{{ item.supply_item.item_code }}</div>
                                            <div class="text-xs text-gray-500">{{ item.supply_item.category.name }}</div>
                                            {% if item.notes %}
                                                <div class="text-xs text-gray-500 mt-1">{{ item.notes }}</div>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ item.quantity_approved }} {{ item.supply_item.unit_of_measure }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="max-w-24">
                                                {{ form|get_field:release_field }}
                                                {% if form.errors|get_item:release_field %}
                                                    <div class="text-red-600 text-xs mt-1">
                                                        {{ form.errors|get_item:release_field|join:", " }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                {{ form|get_field:qr_field }}
                                                <label for="{{ form|get_field:qr_field.id_for_label }}" class="ml-2 text-sm text-gray-700">
                                                    Generate QR
                                                </label>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">${{ item.unit_price }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900" id="total-{{ item.id }}">
                                                $<span class="item-total" data-unit-price="{{ item.unit_price }}">{{ item.approved_total_cost }}</span>
                                            </div>
                                        </td>
                                    </tr>
                                {% endwith %}
                            {% endif %}
                        {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center">
                                    <div class="text-gray-500">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"></path>
                                        </svg>
                                        <p class="mt-2 text-sm text-gray-500">No approved items found for release</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="bg-gray-50">
                        <tr>
                            <td colspan="5" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                                Release Total:
                            </td>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">
                                $<span id="release-total">0.00</span>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Release Notes -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Release Notes</h2>
            </div>
            <div class="px-6 py-4">
                {{ form.release_notes }}
                {% if form.release_notes.errors %}
                    <div class="text-red-600 text-sm mt-1">
                        {{ form.release_notes.errors|join:", " }}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- QR Code Information -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">QR Code Generation</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>QR codes will be generated for selected items to enable tracking and verification. Each QR code contains item details, quantities, and request information.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{% url 'request_detail' supply_request.request_id %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"></path>
                </svg>
                Release Items
            </button>
        </div>
    </form>
</div>

<script>
    // Calculate release totals dynamically
    function updateReleaseTotals() {
        let releaseTotal = 0;
        
        document.querySelectorAll('input[name^="release_quantity_"]').forEach(function(input) {
            const quantity = parseFloat(input.value) || 0;
            const unitPrice = parseFloat(input.closest('tr').querySelector('.item-total').dataset.unitPrice) || 0;
            const itemTotal = quantity * unitPrice;
            
            // Update item total
            input.closest('tr').querySelector('.item-total').textContent = itemTotal.toFixed(2);
            
            // Add to release total
            releaseTotal += itemTotal;
        });
        
        // Update release total
        document.getElementById('release-total').textContent = releaseTotal.toFixed(2);
    }
    
    // Add event listeners to quantity inputs
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('input[name^="release_quantity_"]').forEach(function(input) {
            input.addEventListener('input', updateReleaseTotals);
        });
        
        // Initial calculation
        updateReleaseTotals();
    });
</script>
{% endblock %}