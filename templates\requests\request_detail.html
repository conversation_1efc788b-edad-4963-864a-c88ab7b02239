{% extends 'base.html' %}

{% block title %}Request {{ supply_request.request_id }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'home' %}" class="text-gray-700 hover:text-blue-600">Home</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{% url 'request_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Requests</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2">{{ supply_request.request_id }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Request {{ supply_request.request_id }}</h1>
                <p class="mt-2 text-gray-600">Submitted by {{ supply_request.requester.get_full_name|default:supply_request.requester.username }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                    {% if supply_request.status == 'pending' %}bg-yellow-100 text-yellow-800
                    {% elif supply_request.status == 'approved' %}bg-green-100 text-green-800
                    {% elif supply_request.status == 'rejected' %}bg-red-100 text-red-800
                    {% elif supply_request.status == 'released' %}bg-blue-100 text-blue-800
                    {% elif supply_request.status == 'completed' %}bg-gray-100 text-gray-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ supply_request.get_status_display }}
                </span>
                {% if supply_request.status == 'pending' and supply_request.requester == user %}
                    <button hx-delete="{% url 'cancel_request' supply_request.request_id %}"
                            hx-confirm="Are you sure you want to cancel this request?"
                            class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        Cancel Request
                    </button>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Request Details -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Request Details</h2>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Justification</h3>
                        <p class="mt-1 text-sm text-gray-900">{{ supply_request.justification }}</p>
                    </div>
                    
                    {% if supply_request.notes %}
                        <div>
                            <h3 class="text-sm font-medium text-gray-700">Additional Notes</h3>
                            <p class="mt-1 text-sm text-gray-900">{{ supply_request.notes }}</p>
                        </div>
                    {% endif %}
                    
                    {% if supply_request.rejection_reason %}
                        <div>
                            <h3 class="text-sm font-medium text-red-700">Rejection Reason</h3>
                            <p class="mt-1 text-sm text-red-900">{{ supply_request.rejection_reason }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Request Items -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Requested Items</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Item
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Category
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quantity
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Unit Price
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total
                                </th>
                                {% if supply_request.status == 'approved' or supply_request.status == 'released' %}
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Approved
                                    </th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for item in request_items %}
                                <tr>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ item.supply_item.item_code }}</div>
                                        {% if item.notes %}
                                            <div class="text-xs text-gray-500 mt-1">{{ item.notes }}</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ item.supply_item.category.name }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ item.quantity_requested }} {{ item.supply_item.unit_of_measure }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">${{ item.unit_price }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">${{ item.total_cost }}</div>
                                    </td>
                                    {% if supply_request.status == 'approved' or supply_request.status == 'released' %}
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                {% if item.quantity_approved %}
                                                    {{ item.quantity_approved }} {{ item.supply_item.unit_of_measure }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </div>
                                        </td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="bg-gray-50">
                            <tr>
                                <td colspan="4" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                                    Total:
                                </td>
                                <td class="px-6 py-3 text-sm font-medium text-gray-900">
                                    ${{ total_cost }}
                                </td>
                                {% if supply_request.status == 'approved' or supply_request.status == 'released' %}
                                    <td class="px-6 py-3"></td>
                                {% endif %}
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Request Information -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Request Information</h2>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Department</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ supply_request.department }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Requested Date</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ supply_request.requested_date|date:"M d, Y g:i A" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Required Date</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ supply_request.required_date|date:"M d, Y" }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Total Items</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ total_items }} item{{ total_items|pluralize }}</dd>
                    </div>
                    
                    {% if supply_request.approved_by %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Approved By</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ supply_request.approved_by.get_full_name|default:supply_request.approved_by.username }}</dd>
                        </div>
                        
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Approved Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ supply_request.approved_date|date:"M d, Y g:i A" }}</dd>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            {% if user.userprofile.role == 'gso_staff' or user.userprofile.role == 'admin' %}
                {% if supply_request.status == 'pending' %}
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-900">Actions</h2>
                        </div>
                        <div class="px-6 py-4 space-y-3">
                            <button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Approve Request
                            </button>
                            <button class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                Reject Request
                            </button>
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}