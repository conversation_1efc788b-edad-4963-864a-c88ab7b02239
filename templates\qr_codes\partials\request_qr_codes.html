{% if qr_codes %}
<div class="space-y-4">
    {% for qr_code in qr_codes %}
    <div class="border border-gray-200 rounded-lg p-4">
        <div class="flex items-center space-x-4">
            <!-- QR Code Image -->
            <div class="flex-shrink-0">
                {% if qr_code.qr_image %}
                <img src="{{ qr_code.qr_image.url }}" alt="QR Code" class="h-16 w-16 border border-gray-300 rounded">
                {% else %}
                <div class="h-16 w-16 bg-gray-100 border border-gray-300 rounded flex items-center justify-center">
                    <i class="fas fa-qrcode text-gray-400"></i>
                </div>
                {% endif %}
            </div>

            <!-- QR Code Details -->
            <div class="flex-1">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-xs font-medium text-gray-500">Code ID</label>
                        <p class="text-sm font-mono text-gray-900">{{ qr_code.code_id }}</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500">Item</label>
                        <p class="text-sm text-gray-900">{{ qr_code.supply_item.name }}</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500">Generated</label>
                        <p class="text-sm text-gray-900">{{ qr_code.generated_date|date:"M d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500">Scans</label>
                        <p class="text-sm text-gray-900">{{ qr_code.scanned_count }}</p>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex-shrink-0">
                <div class="flex space-x-2">
                    <a href="{% url 'qr_code_detail' qr_code.code_id %}"
                        class="text-blue-600 hover:text-blue-800 text-sm">
                        View
                    </a>
                    <a href="{% url 'qr_code_print_label' qr_code.code_id %}"
                        class="text-green-600 hover:text-green-800 text-sm">
                        Print
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-8">
    <i class="fas fa-qrcode text-gray-400 text-4xl mb-4"></i>
    <p class="text-gray-500">No QR codes found for this request</p>
</div>
{% endif %}