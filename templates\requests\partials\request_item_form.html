<div class="request-item-form bg-gray-50 rounded-lg p-4 border border-gray-200">
    <div class="flex justify-between items-start mb-4">
        <h4 class="text-sm font-medium text-gray-900">Item {{ form_index|add:1 }}</h4>
        {% if form_index > 0 %}
            <button type="button" 
                    class="remove-item text-red-600 hover:text-red-800 text-sm font-medium">
                Remove
            </button>
        {% endif %}
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Supply Item Selection -->
        <div>
            <label for="{{ form.supply_item.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                Supply Item
            </label>
            {{ form.supply_item }}
            {% if form.supply_item.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.supply_item.errors.0 }}
                </div>
            {% endif %}
        </div>
        
        <!-- Quantity -->
        <div>
            <label for="{{ form.quantity_requested.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                Quantity
            </label>
            <input type="number" 
                   name="{{ form.quantity_requested.html_name }}" 
                   id="{{ form.quantity_requested.id_for_label }}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                   placeholder="Enter quantity"
                   min="1"
                   hx-get="{% url 'validate_stock' %}"
                   hx-target="#stock-validation-{{ form_index }}"
                   hx-trigger="keyup changed delay:500ms"
                   hx-include="[name='{{ form.supply_item.html_name }}']"
                   hx-vals="js:{item_id: document.querySelector('[name=&quot;{{ form.supply_item.html_name }}&quot;]').value, quantity: this.value}">
            <div id="stock-validation-{{ form_index }}" class="mt-1"></div>
            {% if form.quantity_requested.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.quantity_requested.errors.0 }}
                </div>
            {% endif %}
        </div>
        
        <!-- Notes -->
        <div>
            <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                Notes
            </label>
            {{ form.notes }}
            {% if form.notes.errors %}
                <div class="mt-1 text-sm text-red-600">
                    {{ form.notes.errors.0 }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Item Details Display -->
    <div id="item-details-{{ form_index }}" class="mt-4 p-3 bg-white rounded border">
        <div class="text-gray-500 text-sm">Select an item to see details</div>
    </div>
    
    <!-- Hidden fields for formset -->
    {% for hidden in form.hidden_fields %}
        {{ hidden }}
    {% endfor %}
</div>