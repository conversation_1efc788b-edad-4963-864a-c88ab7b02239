from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary or form fields"""
    if hasattr(dictionary, 'get'):
        return dictionary.get(key)
    elif hasattr(dictionary, '__getitem__'):
        try:
            return dictionary[key]
        except (<PERSON><PERSON><PERSON><PERSON>, IndexError):
            return None
    return None

@register.filter
def get_field(form, field_name):
    """Get a form field by name"""
    try:
        return form[field_name]
    except KeyError:
        return None