{% if items %}
    <div class="space-y-2">
        {% for item in items %}
            <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-xs font-medium text-gray-700">{{ item.item_code|slice:":2"|upper }}</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">{{ item.name }}</p>
                        <p class="text-xs text-gray-500">{{ item.item_code }} • {{ item.category.name }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ item.current_stock }} units</p>
                        <p class="text-xs {% if item.current_stock == 0 %}text-red-500{% elif item.is_low_stock %}text-yellow-500{% else %}text-green-500{% endif %}">
                            {% if item.current_stock == 0 %}Out of Stock
                            {% elif item.is_low_stock %}Low Stock
                            {% else %}In Stock{% endif %}
                        </p>
                    </div>
                    <a href="{% url 'inventory_item_detail' item.id %}" 
                       class="text-primary-600 hover:text-primary-500">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-4 text-gray-500">
        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <p class="mt-2">No items found for "{{ query }}"</p>
    </div>
{% endif %}