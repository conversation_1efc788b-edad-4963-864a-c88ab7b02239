{% extends 'base.html' %}

{% block title %}{{ page_title }} - Smart Supply Management{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'inventory_dashboard' %}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span class="sr-only">Inventory</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'inventory_dashboard' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Inventory</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">{{ supply_item.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Item Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-lg font-medium text-gray-700">{{ supply_item.item_code|slice:":2"|upper }}</span>
                        </div>
                    </div>
                    <div class="ml-6">
                        <h1 class="text-2xl font-bold text-gray-900">{{ supply_item.name }}</h1>
                        <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ supply_item.item_code }}
                            </span>
                            <span>{{ supply_item.category.name }}</span>
                            <span>{{ supply_item.unit_of_measure }}</span>
                        </div>
                        <p class="mt-2 text-gray-600">{{ supply_item.description }}</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'add_inventory_stock' %}?item={{ supply_item.id }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Stock
                    </a>
                    <a href="{% url 'adjust_inventory_stock' %}?item={{ supply_item.id }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Adjust
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Current Stock -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 {% if supply_item.current_stock == 0 %}text-red-400{% elif supply_item.is_low_stock %}text-yellow-400{% else %}text-green-400{% endif %}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Current Stock</dt>
                            <dd class="text-2xl font-bold {% if supply_item.current_stock == 0 %}text-red-600{% elif supply_item.is_low_stock %}text-yellow-600{% else %}text-green-600{% endif %}">
                                {{ supply_item.current_stock }}
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3">
                    {% if supply_item.current_stock == 0 %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Out of Stock
                        </span>
                    {% elif supply_item.is_low_stock %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Low Stock
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            In Stock
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Minimum Threshold -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Minimum Threshold</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ supply_item.minimum_threshold }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Value -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Value</dt>
                            <dd class="text-2xl font-bold text-green-600">₱{{ supply_item.total_value|floatformat:2 }}</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-sm text-gray-500">@ ₱{{ supply_item.unit_cost|floatformat:2 }} per {{ supply_item.unit_of_measure }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Statistics -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Transaction Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ transaction_stats.total_in }}</div>
                    <div class="text-sm text-gray-500">Total Stock In</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">{{ transaction_stats.total_out }}</div>
                    <div class="text-sm text-gray-500">Total Stock Out</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">{{ transaction_stats.adjustments }}</div>
                    <div class="text-sm text-gray-500">Net Adjustments</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Transaction History</h3>
                
                <!-- Filters -->
                <div class="flex space-x-4">
                    <select name="transaction_type" 
                            class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 rounded-md"
                            hx-get="{% url 'inventory_item_detail' supply_item.id %}"
                            hx-trigger="change"
                            hx-target="#transaction-history"
                            hx-include="[name='date_from'], [name='date_to']">
                        <option value="">All Types</option>
                        {% for value, label in transaction_types %}
                            <option value="{{ value }}" {% if filters.transaction_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                    
                    <input type="date" 
                           name="date_from" 
                           value="{{ filters.date_from|default:'' }}"
                           class="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                           hx-get="{% url 'inventory_item_detail' supply_item.id %}"
                           hx-trigger="change"
                           hx-target="#transaction-history"
                           hx-include="[name='transaction_type'], [name='date_to']">
                    
                    <input type="date" 
                           name="date_to" 
                           value="{{ filters.date_to|default:'' }}"
                           class="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                           hx-get="{% url 'inventory_item_detail' supply_item.id %}"
                           hx-trigger="change"
                           hx-target="#transaction-history"
                           hx-include="[name='transaction_type'], [name='date_from']">
                </div>
            </div>
            
            <div id="transaction-history">
                {% include 'inventory/partials/transaction_history.html' %}
            </div>
        </div>
    </div>

    <!-- Recent Requests -->
    {% if recent_requests %}
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Requests</h3>
                <div class="space-y-3">
                    {% for request_item in recent_requests %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <a href="{% url 'request_detail' request_item.request.request_id %}" 
                                   class="text-sm font-medium text-primary-600 hover:text-primary-500">
                                    {{ request_item.request.request_id }}
                                </a>
                                <p class="text-xs text-gray-500">
                                    by {{ request_item.request.requester.get_full_name|default:request_item.request.requester.username }}
                                    • {{ request_item.request.requested_date|timesince }} ago
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">{{ request_item.quantity_requested }} units</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if request_item.request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif request_item.request.status == 'approved' %}bg-green-100 text-green-800
                                    {% elif request_item.request.status == 'rejected' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ request_item.request.get_status_display }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}