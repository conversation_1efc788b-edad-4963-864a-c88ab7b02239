#!/usr/bin/env python
"""
Simple verification script for authentication system
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from django.contrib.auth.models import User
from suptrack.models import UserProfile
from suptrack.decorators import is_admin, is_gso_staff, is_department_user

def verify_auth_system():
    print("🔐 Verifying Authentication System Components")
    print("=" * 50)
    
    # Check if models are working
    print("\n1. Testing Models...")
    try:
        user_count = User.objects.count()
        profile_count = UserProfile.objects.count()
        print(f"   ✅ Users in database: {user_count}")
        print(f"   ✅ UserProfiles in database: {profile_count}")
    except Exception as e:
        print(f"   ❌ Model test failed: {e}")
    
    # Check role functions
    print("\n2. Testing Role Functions...")
    try:
        if UserProfile.objects.exists():
            test_user = UserProfile.objects.first().user
            print(f"   ✅ Testing with user: {test_user.username}")
            print(f"   ✅ Role: {test_user.userprofile.get_role_display()}")
            print(f"   ✅ Is admin: {is_admin(test_user)}")
            print(f"   ✅ Is GSO staff: {is_gso_staff(test_user)}")
            print(f"   ✅ Is department user: {is_department_user(test_user)}")
        else:
            print("   ⚠️  No users found to test roles")
    except Exception as e:
        print(f"   ❌ Role function test failed: {e}")
    
    # Check URL patterns
    print("\n3. Testing URL Patterns...")
    try:
        from django.urls import reverse
        urls = [
            'register', 'login', 'logout', 'profile', 
            'change_password', 'password_reset'
        ]
        for url_name in urls:
            try:
                url = reverse(url_name)
                print(f"   ✅ {url_name}: {url}")
            except Exception as e:
                print(f"   ❌ {url_name}: {e}")
    except Exception as e:
        print(f"   ❌ URL pattern test failed: {e}")
    
    # Check middleware
    print("\n4. Testing Middleware...")
    try:
        from suptrack.middleware import (
            RoleVerificationMiddleware, 
            SessionSecurityMiddleware,
            AuditLogMiddleware
        )
        print("   ✅ RoleVerificationMiddleware imported")
        print("   ✅ SessionSecurityMiddleware imported")
        print("   ✅ AuditLogMiddleware imported")
    except Exception as e:
        print(f"   ❌ Middleware test failed: {e}")
    
    # Check forms
    print("\n5. Testing Forms...")
    try:
        from suptrack.forms import (
            CustomUserRegistrationForm,
            CustomLoginForm,
            UserProfileForm
        )
        print("   ✅ CustomUserRegistrationForm imported")
        print("   ✅ CustomLoginForm imported")
        print("   ✅ UserProfileForm imported")
    except Exception as e:
        print(f"   ❌ Forms test failed: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Authentication System Verification Complete!")
    print("\nImplemented Components:")
    print("• User registration with role assignment")
    print("• Role-based permission decorators")
    print("• Login/logout with session management")
    print("• Password reset functionality")
    print("• User profile management")
    print("• Security middleware")
    print("• Admin user management")

if __name__ == '__main__':
    verify_auth_system()