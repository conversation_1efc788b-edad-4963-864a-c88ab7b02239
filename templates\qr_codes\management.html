{% extends 'base.html' %}
{% load static %}

{% block title %}QR Code Management{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">QR Code Management</h1>
                    <p class="mt-2 text-gray-600">Manage and track QR codes for supply items</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{% url 'qr_scanner_interface' %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-qrcode mr-2"></i>
                        QR Scanner
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-qrcode text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total QR Codes</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_qr_codes }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-eye text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Scanned Codes</p>
                        <p class="text-2xl font-bold text-gray-900">{{ scanned_qr_codes }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-percentage text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Usage Rate</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% if total_qr_codes > 0 %}
                                {{ scanned_qr_codes|floatformat:0 }}%
                            {% else %}
                                0%
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="search" 
                               value="{{ search_query|default:'' }}"
                               placeholder="Search by QR code ID, item name, or request ID..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                    {% if search_query %}
                        <a href="{% url 'qr_code_management' %}" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            Clear
                        </a>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- QR Codes Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">QR Codes</h2>
            </div>
            
            {% if page_obj %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    QR Code
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Item Details
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Request Info
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Scan Activity
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for qr_code in page_obj %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            {% if qr_code.qr_image %}
                                                <img src="{{ qr_code.qr_image.url }}" 
                                                     alt="QR Code" 
                                                     class="h-12 w-12 border border-gray-300 rounded mr-3">
                                            {% endif %}
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ qr_code.code_id }}</div>
                                                <div class="text-sm text-gray-500">{{ qr_code.generated_date|date:"M d, Y" }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ qr_code.supply_item.name }}</div>
                                        <div class="text-sm text-gray-500">{{ qr_code.supply_item.item_code }}</div>
                                        <div class="text-sm text-gray-500">{{ qr_code.supply_item.category.name }}</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">{{ qr_code.request_item.request.request_id }}</div>
                                        <div class="text-sm text-gray-500">{{ qr_code.request_item.request.department }}</div>
                                        <div class="text-sm text-gray-500">{{ qr_code.request_item.request.requester.get_full_name|default:qr_code.request_item.request.requester.username }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <span class="font-medium">{{ qr_code.scanned_count }}</span> scans
                                        </div>
                                        {% if qr_code.last_scanned %}
                                            <div class="text-sm text-gray-500">
                                                Last: {{ qr_code.last_scanned|date:"M d, Y H:i" }}
                                            </div>
                                        {% else %}
                                            <div class="text-sm text-gray-500">Never scanned</div>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{% url 'qr_code_detail' qr_code.code_id %}" 
                                               class="text-blue-600 hover:text-blue-900">
                                                View
                                            </a>
                                            <a href="{% url 'qr_code_print_label' qr_code.code_id %}" 
                                               class="text-green-600 hover:text-green-900">
                                                Print
                                            </a>
                                            <a href="{% url 'regenerate_qr_code' qr_code.code_id %}" 
                                               class="text-orange-600 hover:text-orange-900">
                                                Regenerate
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Previous
                                </a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">{{ page_obj.start_index }}</span>
                                    to <span class="font-medium">{{ page_obj.end_index }}</span>
                                    of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    {% if page_obj.has_previous %}
                                        <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    {% endif %}
                                    
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                                {{ num }}
                                            </span>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                {{ num }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if page_obj.has_next %}
                                        <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    {% endif %}
                                </nav>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-qrcode text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No QR codes found</h3>
                    <p class="text-gray-500">
                        {% if search_query %}
                            No QR codes match your search criteria.
                        {% else %}
                            QR codes will appear here when items are released.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>

        <!-- Recent Scans -->
        {% if recent_scans %}
            <div class="mt-8 bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Scans</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        {% for qr_code in recent_scans %}
                            <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-qrcode text-gray-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ qr_code.supply_item.name }}</p>
                                        <p class="text-sm text-gray-500">{{ qr_code.code_id }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-900">{{ qr_code.last_scanned|date:"M d, Y H:i" }}</p>
                                    <p class="text-sm text-gray-500">{{ qr_code.scanned_count }} total scans</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}