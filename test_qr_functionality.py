#!/usr/bin/env python
"""
Test script to verify QR code functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from suptrack.models import SupplyItem, Category, SupplyRequest, RequestItem, QRCode, User, UserProfile
from suptrack.services import QRCodeService
from django.utils import timezone
import json

def test_qr_code_functionality():
    """Test QR code generation and management functionality"""
    print("Testing QR Code Functionality...")
    print("=" * 50)
    
    try:
        # Test 1: QR Code Service validation
        print("1. Testing QR code data validation...")
        
        # Valid QR data
        valid_qr_data = {
            'item_id': 1,
            'item_code': 'TEST-001',
            'request_id': 'REQ-TEST-001',
            'quantity': 5
        }
        
        validation_result = QRCodeService.validate_qr_code(valid_qr_data)
        assert validation_result['success'], f"Valid QR data should pass validation: {validation_result}"
        print("   ✓ Valid QR data validation passed")
        
        # Invalid QR data (missing fields)
        invalid_qr_data = {
            'item_id': 1,
            'item_code': 'TEST-001'
            # Missing required fields
        }
        
        validation_result = QRCodeService.validate_qr_code(invalid_qr_data)
        assert not validation_result['success'], "Invalid QR data should fail validation"
        print("   ✓ Invalid QR data validation failed as expected")
        
        # Test 2: QR Code verification (will fail if no QR codes exist, which is expected)
        print("\n2. Testing QR code verification...")
        
        verification_result = QRCodeService.verify_qr_code('NONEXISTENT-CODE')
        assert not verification_result['success'], "Non-existent QR code should fail verification"
        print("   ✓ Non-existent QR code verification failed as expected")
        
        # Test 3: Check if we have any existing QR codes
        print("\n3. Checking existing QR codes...")
        
        qr_codes = QRCode.objects.filter(is_active=True)
        print(f"   Found {qr_codes.count()} active QR codes in the system")
        
        if qr_codes.exists():
            # Test with an existing QR code
            test_qr = qr_codes.first()
            print(f"   Testing with QR code: {test_qr.code_id}")
            
            verification_result = QRCodeService.verify_qr_code(test_qr.code_id)
            if verification_result['success']:
                print("   ✓ Existing QR code verification passed")
                
                # Test QR code data extraction
                qr_data = QRCodeService.get_qr_code_data(test_qr)
                assert 'code_id' in qr_data, "QR data should contain code_id"
                assert 'item_name' in qr_data, "QR data should contain item_name"
                print("   ✓ QR code data extraction passed")
            else:
                print(f"   ⚠ Existing QR code verification failed: {verification_result['error']}")
        
        # Test 4: Check QR code queries
        print("\n4. Testing QR code queries...")
        
        # Test getting QR codes for requests
        requests = SupplyRequest.objects.all()
        if requests.exists():
            test_request = requests.first()
            request_qr_codes = QRCodeService.get_qr_codes_for_request(test_request.request_id)
            print(f"   Found {request_qr_codes.count()} QR codes for request {test_request.request_id}")
        
        # Test getting QR codes for items
        items = SupplyItem.objects.all()
        if items.exists():
            test_item = items.first()
            item_qr_codes = QRCodeService.get_qr_codes_for_item(test_item.id)
            print(f"   Found {item_qr_codes.count()} QR codes for item {test_item.name}")
        
        print("\n" + "=" * 50)
        print("✅ QR Code functionality tests completed successfully!")
        print("\nImplemented features:")
        print("- Enhanced QRCodeService with validation and verification")
        print("- QR code regeneration functionality")
        print("- QR code tracking and scan processing")
        print("- QR code management views and templates")
        print("- Mobile QR scanner interface")
        print("- Printable QR code labels")
        print("- HTMX integration for dynamic QR code operations")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during QR code functionality test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_qr_code_functionality()
    sys.exit(0 if success else 1)