<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Smart Supply Management System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC" crossorigin="anonymous"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- QR Code Scanner (for mobile QR functionality) -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/minified/html5-qrcode.min.js"></script>
    
    <!-- Custom styles -->
    <style>
        /* Loading states for HTMX */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        
        /* Loading indicator for request list */
        .htmx-indicator-parent {
            position: relative;
        }
        .htmx-request.htmx-indicator-parent .htmx-indicator {
            opacity: 1;
        }
        .htmx-request.htmx-indicator {
            opacity: 1;
        }
        
        /* Mobile-first responsive utilities */
        @media (max-width: 768px) {
            .mobile-hidden {
                display: none;
            }
        }
        
        /* Touch-friendly button sizing */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased" hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>
    <!-- Loading indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-0 left-0 w-full h-1 bg-primary-500 z-50">
        <div class="h-full bg-primary-600 animate-pulse"></div>
    </div>
    
    <!-- Mobile-responsive layout container -->
    <div class="min-h-screen flex flex-col">
        {% if user.is_authenticated %}
            <!-- Navigation -->
            <nav class="bg-white shadow-sm border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <div class="flex-shrink-0 flex items-center">
                                <a href="{% url 'home' %}" class="text-xl font-bold text-gray-900">Smart Supply</a>
                            </div>
                            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                                <a href="{% url 'request_dashboard' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    Dashboard
                                </a>
                                <a href="{% url 'request_list' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    My Requests
                                </a>
                                <a href="{% url 'qr_scanner_interface' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                    QR Scanner
                                </a>
                                {% if user.userprofile.role == 'gso_staff' or user.userprofile.role == 'admin' %}
                                    <a href="{% url 'pending_requests_dashboard' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        Pending Requests
                                    </a>
                                    <a href="{% url 'approved_requests_list' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        Approved Requests
                                    </a>
                                    <a href="{% url 'inventory_dashboard' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        Inventory
                                    </a>
                                    <a href="{% url 'qr_code_management' %}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                        QR Codes
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="hidden sm:ml-6 sm:flex sm:items-center">
                            <div class="ml-3 relative">
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm text-gray-700">{{ user.get_full_name|default:user.username }}</span>
                                    <a href="{% url 'profile' %}" class="text-gray-400 hover:text-gray-500">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </a>
                                    <a href="{% url 'logout' %}" class="text-gray-400 hover:text-gray-500">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        {% endif %}
        
        {% block layout %}
        <!-- Main content area -->
        <main class="flex-1 p-4 lg:p-6">
            <!-- Messages -->
            {% if messages %}
                <div class="mb-4 space-y-2">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} p-4 rounded-lg border {% if message.tags == 'error' %}bg-red-50 border-red-200 text-red-800{% elif message.tags == 'success' %}bg-green-50 border-green-200 text-green-800{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800{% else %}bg-blue-50 border-blue-200 text-blue-800{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            {% block content %}{% endblock %}
        </main>
        {% endblock %}
    </div>
    
    <!-- HTMX configuration -->
    <script>
        // Configure HTMX
        htmx.config.globalViewTransitions = true;
        htmx.config.useTemplateFragments = true;
        htmx.config.timeout = 10000; // 10 second timeout
        htmx.config.defaultSwapStyle = 'outerHTML';
        
        // HTMX event handlers
        document.body.addEventListener('htmx:configRequest', function(evt) {
            // Ensure CSRF token is included in all HTMX requests
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                             document.querySelector('[name=csrfmiddlewaretoken]')?.value;
            if (csrfToken) {
                evt.detail.headers['X-CSRFToken'] = csrfToken;
            }
        });
        
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            // Show loading indicator
            const indicator = document.getElementById('loading-indicator');
            if (indicator) indicator.style.display = 'block';
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            // Hide loading indicator
            const indicator = document.getElementById('loading-indicator');
            if (indicator) indicator.style.display = 'none';
        });
        
        // Error handling
        document.body.addEventListener('htmx:responseError', function(evt) {
            console.error('HTMX Error:', evt.detail);
            showNotification('An error occurred. Please try again.', 'error');
        });
        
        document.body.addEventListener('htmx:timeout', function(evt) {
            showNotification('Request timed out. Please check your connection.', 'warning');
        });
        
        // Utility function for notifications
        function showNotification(message, type = 'info') {
            const colors = {
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                success: 'bg-green-500',
                info: 'bg-blue-500'
            };
            
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 ${colors[type]} text-white p-4 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }
        
        // Make showNotification globally available
        window.showNotification = showNotification;
    </script>
    
    <!-- Unpoly configuration -->
    <script>
        // Configure Unpoly for progressive enhancement
        up.feedback.config.currentClasses = ['bg-primary-100', 'text-primary-800'];
        up.form.config.submitButtons = ['[type=submit]', 'button[form]'];
        
        // Mobile-friendly navigation
        up.viewport.config.viewports = ['[up-viewport]', '.main-content'];
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>