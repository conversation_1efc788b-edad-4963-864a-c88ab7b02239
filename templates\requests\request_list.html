{% extends 'base.html' %}

{% block title %}Supply Requests - {{ block.super }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Supply Requests</h1>
                <p class="mt-2 text-gray-600">Manage and track your supply requests</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'quick_request' %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Quick Request
                </a>
                <a href="{% url 'create_request' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    New Request
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Requests</h2>
            <form class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
                  hx-get="{% url 'request_list' %}"
                  hx-target="#request-list"
                  hx-trigger="change, keyup changed delay:500ms from:input[type=text]">
                <div>
                    <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Status
                    </label>
                    {{ filter_form.status }}
                </div>
                
                <div>
                    <label for="{{ filter_form.date_from.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        From Date
                    </label>
                    {{ filter_form.date_from }}
                </div>
                
                <div>
                    <label for="{{ filter_form.date_to.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        To Date
                    </label>
                    {{ filter_form.date_to }}
                </div>
                
                <div>
                    <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search
                    </label>
                    {{ filter_form.search }}
                </div>
            </form>
        </div>
    </div>

    <!-- Request List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div id="request-list" class="htmx-indicator-parent">
            <div class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <div class="flex items-center space-x-2">
                    <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-sm text-gray-600">Loading requests...</span>
                </div>
            </div>
            {% include 'requests/partials/request_list.html' %}
        </div>
    </div>
</div>

<script>
    // Handle request updates
    document.body.addEventListener('requestUpdated', function() {
        // Refresh the request list
        htmx.trigger('#request-list', 'refresh');
    });
</script>
{% endblock %}