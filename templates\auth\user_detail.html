{% extends 'base.html' %}
{% load static %}

{% block title %}User Details - {{ profile_user.get_full_name|default:profile_user.username }} - Smart Supply Management{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'user_management' %}" class="text-gray-400 hover:text-gray-500">
                            <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                            </svg>
                            <span class="sr-only">Back</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <a href="{% url 'user_management' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">User Management</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">{{ profile_user.get_full_name|default:profile_user.username }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-4 flex items-center justify-between">
            <h1 class="text-3xl font-bold text-gray-900">User Details</h1>
            <div class="flex space-x-3">
                <button onclick="toggleUserStatus({{ profile_user.id }}, '{{ profile_user.username }}', {{ profile.is_active|yesno:'true,false' }})"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                        {% if profile.is_active %}bg-red-600 hover:bg-red-700{% else %}bg-green-600 hover:bg-green-700{% endif %} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    {% if profile.is_active %}Deactivate User{% else %}Activate User{% endif %}
                </button>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Basic Information</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Personal details and account information.</p>
                </div>
                <div class="border-t border-gray-200">
                    <dl>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Full name</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                {{ profile_user.get_full_name|default:"Not provided" }}
                            </dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Username</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ profile_user.username }}</dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Email address</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ profile_user.email|default:"Not provided" }}</dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Role</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if profile.role == 'admin' %}bg-purple-100 text-purple-800
                                    {% elif profile.role == 'gso_staff' %}bg-blue-100 text-blue-800
                                    {% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ profile.get_role_display }}
                                </span>
                            </dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Department</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ profile.department }}</dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Phone number</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ profile.phone_number|default:"Not provided" }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Activity</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Recent supply requests and transactions.</p>
                </div>
                <div class="border-t border-gray-200">
                    {% if recent_requests or recent_transactions %}
                        <div class="divide-y divide-gray-200">
                            {% for request in recent_requests %}
                                <div class="px-4 py-4">
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm font-medium text-gray-900">
                                            Supply Request: {{ request.request_id }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ request.requested_date|date:"M d, Y" }}
                                        </div>
                                    </div>
                                    <div class="mt-1 text-sm text-gray-600">
                                        Status: <span class="font-medium">{{ request.get_status_display }}</span>
                                    </div>
                                </div>
                            {% endfor %}
                            
                            {% for transaction in recent_transactions %}
                                <div class="px-4 py-4">
                                    <div class="flex items-center justify-between">
                                        <div class="text-sm font-medium text-gray-900">
                                            Transaction: {{ transaction.transaction_id }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ transaction.timestamp|date:"M d, Y" }}
                                        </div>
                                    </div>
                                    <div class="mt-1 text-sm text-gray-600">
                                        {{ transaction.get_transaction_type_display }}: {{ transaction.supply_item.name }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="px-4 py-8 text-center text-gray-500">
                            No recent activity found.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Account Status -->
        <div class="space-y-6">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Account Status</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                    <dl class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if profile.is_active %}bg-green-100 text-green-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {% if profile.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Member since</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ profile_user.date_joined|date:"F d, Y" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last login</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {% if profile_user.last_login %}
                                    {{ profile_user.last_login|date:"F d, Y g:i A" }}
                                {% else %}
                                    Never
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Profile updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ profile.updated_at|date:"F d, Y g:i A" }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for user status toggle -->
<script>
function toggleUserStatus(userId, username, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const message = `Are you sure you want to ${action} user "${username}"?`;
    
    if (confirm(message)) {
        fetch(`/admin/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the page to reflect changes
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating user status.');
        });
    }
}
</script>

{% csrf_token %}
{% endblock %}