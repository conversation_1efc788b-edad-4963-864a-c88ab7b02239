#!/usr/bin/env python
"""
Test script to verify inventory management functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smartsupply.settings')
django.setup()

from suptrack.models import Category, SupplyItem, StockTransaction, UserProfile
from django.contrib.auth.models import User

def test_inventory_models():
    """Test inventory models and relationships"""
    print("Testing inventory models...")
    
    # Check if we have categories
    categories = Category.objects.filter(is_active=True)
    print(f"Found {categories.count()} active categories")
    
    # Check if we have supply items
    items = SupplyItem.objects.filter(is_active=True)
    print(f"Found {items.count()} active supply items")
    
    # Check for low stock items
    low_stock_items = SupplyItem.objects.filter(
        is_active=True,
        current_stock__lte=django.db.models.F('minimum_threshold')
    )
    print(f"Found {low_stock_items.count()} low stock items")
    
    # Check transactions
    transactions = StockTransaction.objects.all()
    print(f"Found {transactions.count()} stock transactions")
    
    # Display some sample items
    print("\nSample inventory items:")
    for item in items[:5]:
        status = "Out of Stock" if item.current_stock == 0 else "Low Stock" if item.is_low_stock else "In Stock"
        print(f"- {item.name} ({item.item_code}): {item.current_stock} units - {status}")
    
    print("\nInventory models test completed successfully!")

def test_inventory_views():
    """Test that inventory views can be imported"""
    print("Testing inventory view imports...")
    
    try:
        from suptrack.views import (
            inventory_dashboard, inventory_item_detail, add_inventory_stock,
            adjust_inventory_stock, low_stock_alerts, inventory_search,
            get_inventory_status, get_item_stock_info
        )
        print("All inventory views imported successfully!")
    except ImportError as e:
        print(f"Error importing inventory views: {e}")
        return False
    
    return True

def test_inventory_urls():
    """Test that inventory URLs are configured"""
    print("Testing inventory URL configuration...")
    
    from django.urls import reverse
    
    try:
        urls_to_test = [
            'inventory_dashboard',
            'add_inventory_stock',
            'adjust_inventory_stock',
            'low_stock_alerts',
            'inventory_search',
            'get_inventory_status',
            'get_item_stock_info'
        ]
        
        for url_name in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"✓ {url_name}: {url}")
            except Exception as e:
                print(f"✗ {url_name}: {e}")
        
        # Test item detail URL (requires parameter)
        if SupplyItem.objects.exists():
            item = SupplyItem.objects.first()
            url = reverse('inventory_item_detail', kwargs={'item_id': item.id})
            print(f"✓ inventory_item_detail: {url}")
        
        print("URL configuration test completed!")
        
    except Exception as e:
        print(f"Error testing URLs: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== Inventory Management System Test ===\n")
    
    # Test models
    test_inventory_models()
    print()
    
    # Test views
    if test_inventory_views():
        print()
        
        # Test URLs
        test_inventory_urls()
    
    print("\n=== Test completed ===")