{% extends 'base.html' %}
{% load static %}

{% block title %}QR Code Scanner{% endblock %}

{% block extra_head %}
<style>
    #qr-reader {
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }
    
    #qr-reader__dashboard_section_csr {
        display: none !important;
    }
    
    .scan-result {
        animation: fadeIn 0.3s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .scanner-overlay {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .scanner-frame {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 200px;
        height: 200px;
        border: 2px solid #3B82F6;
        border-radius: 8px;
        pointer-events: none;
        z-index: 10;
    }
    
    .scanner-frame::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid rgba(59, 130, 246, 0.3);
        border-radius: 8px;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="qrScanner()">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">QR Code Scanner</h1>
                    <p class="mt-2 text-gray-600">Scan QR codes for item tracking and verification</p>
                </div>
                <div class="flex space-x-4">
                    <button @click="toggleScanner()" 
                            :class="scannerActive ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
                            class="text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i :class="scannerActive ? 'fas fa-stop' : 'fas fa-play'" class="mr-2"></i>
                        <span x-text="scannerActive ? 'Stop Scanner' : 'Start Scanner'"></span>
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Scanner Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Camera Scanner</h2>
                
                <!-- Scan Type Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Scan Type</label>
                    <select x-model="scanType" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        {% for value, label in scan_types %}
                            <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Scanner Container -->
                <div class="scanner-overlay bg-gray-100 rounded-lg mb-4">
                    <div id="qr-reader" class="relative">
                        <div x-show="!scannerActive" class="text-center py-20">
                            <i class="fas fa-qrcode text-gray-400 text-6xl mb-4"></i>
                            <p class="text-gray-500">Click "Start Scanner" to begin scanning QR codes</p>
                        </div>
                    </div>
                    <div class="scanner-frame" x-show="scannerActive"></div>
                </div>

                <!-- Scanner Status -->
                <div class="text-center">
                    <div x-show="scannerActive" class="text-green-600 font-medium">
                        <i class="fas fa-circle animate-pulse mr-2"></i>
                        Scanner Active - Point camera at QR code
                    </div>
                    <div x-show="!scannerActive && !lastScanResult" class="text-gray-500">
                        Scanner Inactive
                    </div>
                </div>

                <!-- Manual Input Option -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Manual QR Code Entry</h3>
                    <div class="flex space-x-2">
                        <input type="text" 
                               x-model="manualCode"
                               placeholder="Enter QR code ID manually..."
                               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <button @click="processManualCode()" 
                                :disabled="!manualCode.trim()"
                                class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors">
                            Process
                        </button>
                    </div>
                </div>
            </div>

            <!-- Scan Results Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Scan Results</h2>
                
                <!-- Loading State -->
                <div x-show="processing" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-500">Processing QR code...</p>
                </div>

                <!-- Success Result -->
                <div x-show="lastScanResult && lastScanResult.success" class="scan-result">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-green-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Scan Successful</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p x-text="lastScanResult?.message"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code Details -->
                    <div x-show="lastScanResult?.qr_data" class="space-y-3">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-500">Code ID:</span>
                                <span class="text-gray-900 font-mono" x-text="lastScanResult?.qr_data?.code_id"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-500">Item:</span>
                                <span class="text-gray-900" x-text="lastScanResult?.qr_data?.item_name"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-500">Item Code:</span>
                                <span class="text-gray-900 font-mono" x-text="lastScanResult?.qr_data?.item_code"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-500">Department:</span>
                                <span class="text-gray-900" x-text="lastScanResult?.qr_data?.department"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-500">Quantity:</span>
                                <span class="text-gray-900" x-text="lastScanResult?.qr_data?.quantity + ' ' + lastScanResult?.qr_data?.unit_of_measure"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-500">Scan Count:</span>
                                <span class="text-gray-900" x-text="lastScanResult?.qr_data?.scan_count"></span>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-2 mt-4">
                            <a :href="'/qr-codes/' + lastScanResult?.qr_data?.code_id + '/'" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                                View Details
                            </a>
                            <a :href="'/qr-codes/' + lastScanResult?.qr_data?.code_id + '/print/'" 
                               class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                                Print Label
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Error Result -->
                <div x-show="lastScanResult && !lastScanResult.success" class="scan-result">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Scan Error</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p x-text="lastScanResult?.error"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Results -->
                <div x-show="!processing && !lastScanResult" class="text-center py-8">
                    <i class="fas fa-qrcode text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">Scan a QR code to see results here</p>
                </div>
            </div>
        </div>

        <!-- Recent Scans -->
        <div class="mt-8 bg-white rounded-lg shadow" x-show="scanHistory.length > 0">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Recent Scans</h2>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <template x-for="scan in scanHistory.slice(0, 5)" :key="scan.timestamp">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i :class="scan.success ? 'fas fa-check-circle text-green-500' : 'fas fa-times-circle text-red-500'"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900" x-text="scan.qr_data?.item_name || 'Unknown Item'"></p>
                                    <p class="text-sm text-gray-500" x-text="scan.qr_data?.code_id || scan.error"></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-900" x-text="new Date(scan.timestamp).toLocaleTimeString()"></p>
                                <p class="text-sm text-gray-500" x-text="scan.scan_type"></p>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function qrScanner() {
    return {
        scannerActive: false,
        scanType: 'general',
        processing: false,
        lastScanResult: null,
        scanHistory: [],
        manualCode: '',
        html5QrCode: null,

        init() {
            // Initialize scanner history from localStorage
            const savedHistory = localStorage.getItem('qr_scan_history');
            if (savedHistory) {
                this.scanHistory = JSON.parse(savedHistory);
            }
        },

        async toggleScanner() {
            if (this.scannerActive) {
                await this.stopScanner();
            } else {
                await this.startScanner();
            }
        },

        async startScanner() {
            try {
                if (!this.html5QrCode) {
                    this.html5QrCode = new Html5Qrcode("qr-reader");
                }

                const config = {
                    fps: 10,
                    qrbox: { width: 200, height: 200 },
                    aspectRatio: 1.0
                };

                await this.html5QrCode.start(
                    { facingMode: "environment" },
                    config,
                    (decodedText, decodedResult) => {
                        this.onScanSuccess(decodedText);
                    },
                    (errorMessage) => {
                        // Ignore scan errors (they happen frequently)
                    }
                );

                this.scannerActive = true;
            } catch (err) {
                console.error('Error starting scanner:', err);
                alert('Error starting camera scanner. Please check camera permissions.');
            }
        },

        async stopScanner() {
            try {
                if (this.html5QrCode && this.scannerActive) {
                    await this.html5QrCode.stop();
                }
                this.scannerActive = false;
            } catch (err) {
                console.error('Error stopping scanner:', err);
            }
        },

        async onScanSuccess(decodedText) {
            // Stop scanner temporarily to prevent multiple scans
            await this.stopScanner();
            
            // Process the scanned QR code
            await this.processScan(decodedText);
            
            // Restart scanner after a delay
            setTimeout(() => {
                if (!this.scannerActive) {
                    this.startScanner();
                }
            }, 2000);
        },

        async processScan(qrData) {
            this.processing = true;
            
            try {
                // First validate the QR data
                const validateResponse = await fetch('/api/qr-codes/validate/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({ qr_data: qrData })
                });

                const validateResult = await validateResponse.json();

                if (validateResult.success) {
                    // Process the scan
                    const scanResponse = await fetch('/api/qr-codes/scan/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: JSON.stringify({ 
                            code_id: validateResult.qr_data.code_id,
                            scan_type: this.scanType 
                        })
                    });

                    const scanResult = await scanResponse.json();
                    this.lastScanResult = scanResult;
                    
                    // Add to history
                    this.addToHistory(scanResult);
                } else {
                    this.lastScanResult = validateResult;
                    this.addToHistory(validateResult);
                }
            } catch (error) {
                console.error('Scan processing error:', error);
                this.lastScanResult = {
                    success: false,
                    error: 'Network error processing scan'
                };
                this.addToHistory(this.lastScanResult);
            } finally {
                this.processing = false;
            }
        },

        async processManualCode() {
            if (!this.manualCode.trim()) return;
            
            await this.processScan(this.manualCode.trim());
            this.manualCode = '';
        },

        addToHistory(result) {
            const historyItem = {
                ...result,
                timestamp: new Date().toISOString(),
                scan_type: this.scanType
            };
            
            this.scanHistory.unshift(historyItem);
            
            // Keep only last 20 scans
            if (this.scanHistory.length > 20) {
                this.scanHistory = this.scanHistory.slice(0, 20);
            }
            
            // Save to localStorage
            localStorage.setItem('qr_scan_history', JSON.stringify(this.scanHistory));
        }
    }
}
</script>
{% endblock %}