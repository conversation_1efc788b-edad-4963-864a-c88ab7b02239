{% extends 'base.html' %}
{% load static %}

{% block title %}Set New Password - Smart Supply Management{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2h-3m-3 0h3m0 0V9a2 2 0 00-2-2m0 0a2 2 0 00-2-2V5a2 2 0 012-2h3"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Set new password
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Enter your new password below.
            </p>
        </div>
        
        {% if validlink %}
            <form class="mt-8 space-y-6" method="post">
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700">New Password</label>
                        {{ form.new_password1 }}
                        {% if form.new_password1.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.new_password1.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                        {{ form.new_password2 }}
                        {% if form.new_password2.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.new_password2.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                {% if form.non_field_errors %}
                    <div class="rounded-md bg-red-50 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    {{ form.non_field_errors.0 }}
                                </h3>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Set New Password
                    </button>
                </div>
            </form>
        {% else %}
            <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Invalid Reset Link
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>This password reset link is invalid or has expired. Please request a new password reset.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <a href="{% url 'password_reset' %}" class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    Request New Reset Link
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}